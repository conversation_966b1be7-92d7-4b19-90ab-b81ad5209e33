name: Deploy testing

on:
  push:
    branches:
      - testing

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:

    - name: Deploy to server
      uses: appleboy/ssh-action@v0.1.6
      with:
        host: ${{ secrets.SSH_HOST_DEV }}
        username: ${{ secrets.SSH_USER }}
        port: ${{ secrets.SSH_PORT_DEV }}
        key: ${{ secrets.SSH_KEY_DEV }}
        script: |
          cd workspace/open-ai-chatbot-whatsapp
          git pull
          systemctl restart api.service
          exit
