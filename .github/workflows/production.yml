name: Deploy production

on:
  push:
    branches:
      - production

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:

    - name: Deploy to server
      uses: appleboy/ssh-action@v0.1.6
      with:
        host: ${{ secrets.SSH_HOST_PROD }}
        username: ${{ secrets.SSH_USER }}
        port: ${{ secrets.SSH_PORT_PROD }}
        key: ${{ secrets.SSH_KEY_PROD }}
        script: |
          cd workspace/open-ai-chatbot-whatsapp
          git pull
          systemctl restart api.service
          exit
