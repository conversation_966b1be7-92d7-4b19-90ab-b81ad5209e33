import logging
import redis


class RedisDatabase:
    def __init__(self, host, port, username, password, db):
        self.time_to_expire_s = 86400  # TTL en segundos (1 dia)
        self.connection = redis.Redis(
            host=host,
            port=port,
            username=username,
            password=password,
            db=db,
            decode_responses=True  # Convierte las respuestas en cadenas en lugar de bytes
        )

    def set_key_value(self, key, value):
        try:
            result = self.connection.set(name=key, value=value, ex=self.time_to_expire_s)
            logging.debug(f"Set result: {result}")
        except Exception as e:
            logging.error(f"Error during key-value set: {str(e)}")

    def get_value(self, key):
        try:
            value = self.connection.get(key)
            logging.debug(f"Get result for key {key}: {value}")
            return value
        except Exception as e:
            logging.error(f"Error during key retrieval: {str(e)}")
            return None

    def delete_key(self, key):
        try:
            self.connection.delete(key)
        except Exception as e:
            logging.error(f"Error during key-value delete: {str(e)}")
