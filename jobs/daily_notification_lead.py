import logging
import os
import requests
from datetime import datetime, timedelta
from dotenv import load_dotenv
from db.mongo import MongoDatabase

load_dotenv()

MONGO_URI = os.environ.get("MONGO_URI")
MONGO_HOST = os.environ.get("MONGO_HOST")
MONGO_DB = os.environ.get("MONGO_DB")
MONGO_USER = os.environ.get("MONGO_USER")
MONGO_PASSWORD = os.environ.get("MONGO_PASSWORD")
DISCORD_WEBHOOK_URL = os.environ.get("DISCORD_WEBHOOK_URL")

# Lista hardcodeada de IDs de clientes con sus nombres
CLIENTS = [
    {"id": "66438f2e8a7313f4aebf00d1", "name": "NutriBeauty"},
    {"id": "66e1df36562faa59df8c1c82", "name": "Center Suites"},
    {"id": "66e1bfd0562faa59df8c1b91", "name": "Vitte - Br"},
    {"id": "66e1a5d4562faa59df8c1a21", "name": "Vitte - Arg"},
    {"id": "67ade8c7ef592f26c58f148f", "name": "Prosegur - Sandra"},
    {"id": "67ae0286ef592f26c58f16cc", "name": "Prosegur - Karina"},
    {"id": "6706ec02c98b0d89c3c5b7e1", "name": "Prosegur - Aldana"},
]

def send_discord_notification(client_name, created_count, updated_count):
    message = f"Client: {client_name}\nNew Conversations: {created_count}\nUpdated Conversations: {updated_count}"
    payload = {"content": message}
    requests.post(DISCORD_WEBHOOK_URL, json=payload)

def run():
    folder_path = os.path.abspath(os.path.dirname(__file__))
    log_file_path = os.path.join(folder_path, "job_asignate_lead.log")
    
    logging.basicConfig(
        filename=log_file_path,
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )
    
    try:
        conversations_db = MongoDatabase(
            MONGO_URI, MONGO_USER, MONGO_PASSWORD, MONGO_HOST, MONGO_DB, "conversations"
        )
        
        # Obtener la fecha de hace 24 horas
        last_24_hours = datetime.utcnow() - timedelta(days=1)
        
        for client in CLIENTS:
            client_id = client["id"]
            client_name = client["name"]
            
            created_conversations = conversations_db.get_documents_by_filter(
                {"clientid": client_id, "createdAt": {"$gte": last_24_hours}},
                {"_id": 1},
            )
            
            updated_conversations = conversations_db.get_documents_by_filter(
                {"clientid": client_id, "updatedAt": {"$gte": last_24_hours}},
                {"_id": 1},
            )
            
            created_count = len(created_conversations) if created_conversations else 0
            updated_count = len(updated_conversations) if updated_conversations else 0
            
            send_discord_notification(client_name, created_count, updated_count)
        
        logging.info("Job completed successfully")
    except Exception as e:
        logging.error(f"Error occurred: {str(e)}")
        
if __name__ == "__main__":
    run()