# Agregar Job a Crontab Linux

Sigue estos pasos para agregar un script al crontab y programar su ejecución periódica en Linux:

1. Abre una terminal y ejecuta el siguiente comando para abrir el archivo de configuración de crontab:
```
crontab -e
```

2. Añade una nueva línea al archivo con la siguiente sintaxis:
```
0 0 * * * python /ruta/al/script.py
```

Esta línea le indica a crontab que ejecute el comando `python /ruta/al/script.py` todos los días a las 00:00 (medianoche).

Asegúrate de reemplazar `/ruta/al/script.py` con la ruta completa hacia tu script de Python.

3. Guarda y cierra el archivo.

Con estos pasos, el script se ejecutará automáticamente cada 24 horas. Puedes ajustar los valores numéricos de la línea `0 0 * * *` según tus necesidades. Por ejemplo, si deseas ejecutar el script a las 8 a.m., puedes usar `0 8 * * *` en su lugar.

Recuerda que el script debe tener los permisos de ejecución adecuados para que crontab pueda ejecutarlo. Puedes establecer los permisos utilizando el siguiente comando:
```
chmod +x /ruta/al/script.py
```
Con esta configuración, crontab se encargará de ejecutar automáticamente tu script cada 24 horas, actualizando los leads según las condiciones establecidas.