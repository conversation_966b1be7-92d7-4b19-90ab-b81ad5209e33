import os
from datetime import datetime, timedelta
from dotenv import load_dotenv
import logging
from db.mongo import MongoDatabase

load_dotenv()

MONGO_URI = os.environ.get('MONGO_URI')
MONGO_HOST = os.environ.get('MONGO_HOST')
MONGO_DB = os.environ.get('MONGO_DB')
MONGO_USER = os.environ.get('MONGO_USER')
MONGO_PASSWORD = os.environ.get('MONGO_PASSWORD')


def run():
    # Obtener la ruta absoluta de la carpeta actual
    folder_path = os.path.abspath(os.path.dirname(__file__))

    # Ruta completa del archivo de registro
    log_file_path = os.path.join(folder_path, 'job_status_finish_lead.log')

    # Configurar el registro de errores
    logging.basicConfig(
        filename=log_file_path,
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )

    try:
        # Instanciar las bases de datos
        conversations_db = MongoDatabase(MONGO_URI, MONGO_USER, MONGO_PASSWORD, MONGO_HOST, MONGO_DB, 'conversations')

        # Obtener la fecha y hora actual
        now = datetime.now()

        # Calcular la fecha y hora límite (2 días antes de ahora)
        limit = now - timedelta(days=2)
        # Si la fecha límite es un sábado o domingo
        # Restar los días adicionales (sábado y domingo)
        if limit.weekday() == 5:  
            limit -= timedelta(days=limit.weekday() - 1)  
        if limit.weekday() == 6:  
            limit -= timedelta(days=limit.weekday() - 2)  

        # Actualizar los leads que cumplen con la condición
        result = conversations_db.update_many(
            {"$and": [
                {"$or": [
                    {"status": "nuevo"},
                    {"status": "proceso"}
                ]},
                {"nextContact": {"$lt": limit}}
            ]},
            {"$set": {"status": "vencido"}}
        )

        # Registrar los leads actualizados
        logging.info(f"Leads actualizados: {result}")

    except Exception as e:
        # Capturar y registrar los errores
        logging.error(f"Ocurrió un error: {str(e)}")

if __name__ == "__main__":
    run()
