from prometheus_client import Counter, Summary, Histogram

# Summary
REQUEST_LATENCY = Summary('api_request_latency_seconds', 'Tiempo de latencia de las solicitudes')

# Counter
REQUEST_COUNT = Counter('api_request_count', 'Número de solicitudes recibidas', ['method', 'endpoint'])
GENERATE_LEAD_ERROR = Counter('api_generate_lead_error', 'Cantidad de errores en la generación de leads')
UPDATE_LEAD_ERROR = Counter('api_update_lead_error', 'Cantidad de errores en la actualizacion de leads')
GENERATE_LEAD_COUNT = Counter('api_generate_lead_count', 'Cantidad de leads generados')
UPDATE_LEAD_COUNT = Counter('api_update_lead_count', 'Cantidad de leads actualizados')

# Histogram
GENERATE_LEAD_LATENCY = Histogram('api_process_latency_seconds', 'Tiempo de ejecución del proceso de generación/actualización de leads')