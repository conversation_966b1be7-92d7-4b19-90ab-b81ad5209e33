class ConversationDTO:
    def __init__(self, document: dict):
        self.id = str(document.get("_id"))
        lead = document.get("lead", {})
        self.lead_name = lead.get("fullname")
        self.lead_email = lead.get("email")
        self.lead_phone = lead.get("phone")
        self.lead_hash = lead.get("hash")
        self.status = document.get("status")
        self.created_at = document.get("createdAt")
        self.updated_at = document.get("updatedAt")
        self.botstop = document.get("botstop")
        self.assign = document.get("assign")
        self.chathistory = document.get("chathistory", [])
