class ClientDataDTO:
    def __init__(self, **kwargs):
        self.__dict__.update(kwargs)

    @classmethod
    def build(cls, client_data):
        """Método de clase para crear un ClientDataDTO a partir de un diccionario."""
        return cls(**client_data)

    def get(self, key, default=None):
        """Método para obtener un valor de forma segura."""
        return self.__dict__.get(key, default)
