import asyncio
import logging
import threading
from connectors.connector import Connector
from flask import request, make_response
from api.api import WhatsApp
from dtos.client_data_dto import ClientDataDTO
from dtos.crm_message_dto import MessageDTO
from message.message_handler import MessageHandler
from message.factory.message_factory import MessageFactory

VERIFY_TOKEN = "12345"


class WhatsappConnector:
    def __init__(self, conversations_service, cache_service, client_service):
        self.conversations_service = conversations_service
        self.cache_service = cache_service
        self.conversations = {}
        self.conversations_lock = threading.Lock()
        self.client_service = client_service
        self.message_handler = MessageHandler(
            conversations=self.conversations,
            conversations_service=self.conversations_service,
            cache_service=cache_service
        )

    def verify_token(self):
        if request.args.get("hub.verify_token") == VERIFY_TOKEN:
            logging.info("Verified webhook")
            response = make_response(request.args.get("hub.challenge"), 200)
            response.mimetype = "text/plain"
            return response
        logging.error("Webhook Verification failed")
        return "Invalid verification token"

    def process_queue_message(self, data, client_data_dto: ClientDataDTO):
        messenger = WhatsApp(token=client_data_dto.get("token"), phone_number_id=client_data_dto.get("phoneNumberId"))
        changed_field = messenger.changed_field(data)
        if changed_field == "messages":
            new_message = messenger.is_message(data)
            if new_message:
                self.handle_new_message(
                    data=data,
                    messenger=messenger,
                    client_data_dto=client_data_dto
                )
            else:
                delivery = messenger.get_delivery(data)
                if delivery:
                    logging.debug(f"Message : {delivery}")
                else:
                    logging.debug("No new message")

    def handle_new_message(self, data, messenger, client_data_dto: ClientDataDTO):
        message_id = messenger.get_message_id(data)
        asyncio.run(messenger.mark_as_read(message_id))
        name = messenger.get_name(data)
        message_type = messenger.get_message_type(data)
        mobile = messenger.get_mobile(data)
        if mobile.startswith('549'):
            mobile = '54' + mobile[3:]
        logging.info(
            f"New Message; sender:{mobile} name:{name} type:{message_type}"
        )
        with self.conversations_lock:
            connector = WhatsappMessageConnector(messenger, mobile)
            message_factory = MessageFactory(
                connector=connector,
                data=data,
                mobile=mobile,
                name=name,
                message_type=message_type,
                client_data_dto=client_data_dto
            )
            try:
                self.message_handler.handle(message_factory)
            except Exception as e:
                logging.error("Error en el manejo del mensaje: %s", str(e))

    def send_message_to_connector(self, message: MessageDTO):
        client_data = self.client_service.get_client_information(message.client_id)
        if client_data is None:
            logging.error(f"Client data not found for client_id: {message.client_id}")
            return

        messenger = WhatsApp(
            token=client_data["token"],
            phone_number_id=client_data["phoneNumberId"]
        )
        connector = WhatsappMessageConnector(
            messenger=messenger,
            mobile=message.phone
        )
        connector.send_message(
            message=message.message,
            mobile=message.phone
        )


class WhatsappMessageConnector(Connector):
    def __init__(self, messenger, mobile):
        self.messenger = messenger
        self.mobile = mobile
        self.origin = "whatsapp"
        self.origin_id = 1

    def send_message(self, message, mobile):
        asyncio.run(self.messenger.send_message(message, mobile))

    def get_message(self, data):
        return self.messenger.get_message(data)

    def send_contacts(self, contacts, notify_number):
        self.messenger.send_contacts(contacts, notify_number)

    def get_audio(self, data):
        audio = self.messenger.get_audio(data)
        audio_id, mime_type = audio["id"], audio["mime_type"]
        audio_url = self.messenger.query_media_url(audio_id)
        audio_filename = self.messenger.download_media(audio_url, mime_type)
        return audio_filename

    def send_audio(self, audio_id, mobile, link):
        self.messenger.send_audio(audio_id, mobile, link)
