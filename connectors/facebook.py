import logging
import requests
import json
import threading
from flask import request
from connectors.connector import Connector
from dtos.client_data_dto import ClientDataD<PERSON>
from message.message_handler import Message<PERSON>andler
from message.factory.message_factory import MessageFactory

VERIFY_TOKEN = "12345"


class FacebookConnector:
    def __init__(self, conversations_service, cache_service):
        self.conversations_service = conversations_service
        self.cache_service = cache_service
        self.conversations = {}
        self.conversations_lock = threading.Lock()
        self.message_handler = MessageHandler(
            conversations=self.conversations,
            conversations_service=self.conversations_service,
            cache_service=cache_service
        )

    def verify_token(self):
        if request.args.get("hub.mode") == "subscribe" and request.args.get("hub.challenge"):
            if not request.args.get("hub.verify_token") == VERIFY_TOKEN:
                return "Verification token missmatch", 403
            return request.args['hub.challenge'], 200
        return "OK", 200

    def process_queue_message(self, data, client_data_dto: ClientDataDTO):
        if data["object"] == "page":
            for entry in data["entry"]:
                for messaging_event in entry["messaging"]:
                    if messaging_event.get("message"):  # someone sent us a message
                        sender_id = messaging_event["sender"][
                            "id"]  # the facebook ID of the person sending you the message
                        recipient_id = messaging_event["recipient"][
                            "id"]  # the recipient's ID, which should be your page's facebook ID
                        message_text = messaging_event["message"]["text"]  # the message's text

                        self.handle_new_message(
                            data=data,
                            messenger=messaging_event,
                            client_data_dto=client_data_dto
                        )

                    if messaging_event.get("delivery"):  # delivery confirmation
                        pass
                    if messaging_event.get("optin"):  # optin confirmation
                        pass
                    if messaging_event.get("postback"):  # user clicked/tapped "postback" button in earlier message
                        pass

    def handle_new_message(self, data, messenger, client_data_dto):
        name = "Prueba"
        # message_type = messenger.get_message_type(data)
        message_type = "text"
        mobile = "1136013722"
        if mobile.startswith('549'):
            mobile = '54' + mobile[3:]
        logging.info(
            f"New Message; sender:{mobile} name:{name} type:{message_type}"
        )
        with self.conversations_lock:
            connector = FacebookMessageConnector(messenger, client_data_dto.get("fbAccesToken"))
            message_factory = MessageFactory(
                connector=connector,
                data=data,
                mobile=mobile,
                name=name,
                message_type=message_type,
                client_data_dto=client_data_dto
            )
            try:
                self.message_handler.handle(message_factory)
            except Exception as e:
                logging.error("Error en el manejo del mensaje: %s", str(e))


class FacebookMessageConnector(Connector):
    def __init__(self, messenger, accessToken):
        # self.messenger = messenger
        self.recipient_id = messenger["sender"]["id"]
        self.page_id = messenger["recipient"]["id"]
        self.messageText = messenger["message"]["text"]
        self.origin = "facebook"
        self.origin_id = 2
        self.accessToken = accessToken

    def send_message(self, message, mobile):
        params = {
            "access_token": self.accessToken
        }
        headers = {
            "Content-Type": "application/json"
        }
        data = json.dumps({
            "recipient": {
                "id": self.recipient_id
            },
            "message": {
                "text": message
            }
        })
        requests.post("https://graph.facebook.com/v2.6/" + self.page_id + "/messages", params=params, headers=headers,
                      data=data)

    def get_mobile(self):
        return "1136013722"

    def get_message(self, data):
        return self.messageText

        # AGREGAR LAS FUNCIONES QUE FALTEN PARA QUE FB ANDE
