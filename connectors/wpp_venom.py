"""
WhatsApp Venom Connector - Handles WhatsApp message processing using Venom.
Provides thread-safe message handling and session management.
"""
import logging
import threading
import requests

from connectors.connector import Connector
from dtos.client_data_dto import ClientDataDTO
from dtos.crm_message_dto import MessageDTO
from events.event_types import SEND_WHATSAPP_MESSAGE
from message.factory.message_factory import MessageFactory
from message.message_handler import MessageHandler
from producers.event_producer import send_event
from services.cache import CacheService
from services.conversation import ConversationService
from utils.session_timer import SessionTimer

class WhatsappVenomConnector:
    def __init__(
            self,
            conversations_service: ConversationService,
            cache_service: CacheService
    ):
        """
        Initialize the WhatsApp Venom connector.
        
        Args:
            conversations_service: Service for managing conversations
            cache_service: Service for caching data
        """
        self.conversations_service = conversations_service
        self.cache_service = cache_service
        self.session_timer = SessionTimer()
        self.message_handler = MessageHandler(
            session_timer=self.session_timer,
            conversations_service=self.conversations_service,
            cache_service=cache_service
        )
        
        self.session_timer.setup_cleanup_timer(interval_seconds=3600)

    def process_queue_message(self, data, client_data_dto: ClientDataDTO):
        """
        Process a new message from the queue in a separate thread.
        
        Args:
            data: Message data
            client_data_dto: Client data transfer object
        """
        thread = threading.Thread(
            target=self.handle_new_message,
            args=(data, client_data_dto),
            daemon=True
        )
        thread.start()

    def handle_new_message(self, data, client_data_dto: ClientDataDTO):
        """
        Handle a new incoming message.
        
        Args:
            data: Message data
            client_data_dto: Client data transfer object
        """
        try:
            message_type = data["messageType"]
            mobile = data["mobile"]
            name = data["name"]
            
            if mobile.startswith('549'):
                mobile = '54' + mobile[3:]
                
            logging.info(
                f"Processing new message - Sender: {mobile}, Name: {name}, Type: {message_type}"
            )
            
            connector = WhatsappVenomMessageConnector(data, mobile, message_type, name)
            message_factory = MessageFactory(
                connector=connector,
                data=data,
                mobile=mobile,
                name=name,
                message_type=message_type,
                client_data_dto=client_data_dto
            )

            self.message_handler.handle(message_factory)
            
        except KeyError as ke:
            logging.error(f"Required fields are missing from the message: {ke}")
        except Exception as e:
            logging.error(f"Unexpected error processing message: {e}", exc_info=True)

    def send_message_to_connector(self, message: MessageDTO):
        # TODO el message_type siempre es text, pero el dia de mañana podemos enviar fotos desde el CRM
        data = {
            'message': message.message,
            'clientId': message.client_id
        }
        connector = WhatsappVenomMessageConnector(
            messenger=data,
            mobile=message.phone,
            message_type="text",
            name="DEFAULT_VENOM_NAME"
        )
        connector.send_message(message=message.message, mobile=message.phone)


class WhatsappVenomMessageConnector(Connector):
    def __init__(self, messenger, mobile, message_type, name):
        self.messenger = messenger
        self.message = messenger['message']
        self.mobile = mobile
        self.origin = "whatsapp"
        self.origin_id = 3
        self.client_id = messenger['clientId']
        self.message_type = message_type
        self.name = name

    def get_mobile(self, data):
        return self.mobile

    def get_message(self, data):
        return self.message

    def send_message(self, message, mobile):
        data = {
            "clientId": self.client_id,
            "message": {
                "message_type": self.message_type,
                "content": message,
                "to": mobile
            }
        }
        try:
            send_event(event_type=SEND_WHATSAPP_MESSAGE, payload={
                "payload": data
            })
            logging.info(f"Message response for mobile {mobile} and client id {self.client_id}")
        except requests.exceptions.RequestException as e:
            logging.error(f"Error sending the socket event in send message: {e}")

    def send_contacts(self, contact, notify_number):
        contact_phone = contact[0]['phones'][0]['phone']
        self.message_type = "contact"
        self.send_message(contact_phone, notify_number)

    def get_audio(self, data):
        return data['message']
