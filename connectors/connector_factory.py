from connectors.facebook import FacebookConnector
from connectors.wpp import WhatsappConnector
from connectors.wpp_venom import WhatsappVenomConnector
from enums.connector_types import ConnectorType


class ConnectorFactory:
    _connectors = {}

    @classmethod
    def initialize(cls, conversations_service, cache_service, client_service):
        """Inicializa los conectores y los almacena en un diccionario."""
        cls._connectors = {
            ConnectorType.WHATSAPP_API: WhatsappConnector(conversations_service, cache_service, client_service),
            ConnectorType.FACEBOOK: FacebookConnector(conversations_service, cache_service),
            ConnectorType.WHATSAPP_VENOM: WhatsappVenomConnector(conversations_service, cache_service),
        }

    @classmethod
    def get_connector(cls, origin_id):
        """Devuelve el conector correspondiente según el origin_id."""
        connector_type = ConnectorType.from_origin_id(origin_id)
        if connector_type is None:
            raise ValueError(f"Origin ID {origin_id} no tiene un conector asignado.")

        return cls._connectors.get(connector_type)

    @classmethod
    def get_all_connectors(cls):
        """Retorna el diccionario completo de conectores."""
        return cls._connectors
