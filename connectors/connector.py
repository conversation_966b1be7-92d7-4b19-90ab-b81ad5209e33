# Abstract Class

class Connector:
    def send_message(self, message, mobile):
        raise NotImplementedError("Subclass must implement this method")

    def get_message(self, data):
        raise NotImplementedError("Subclass must implement this method")

    def get_interactive_response(self):
        raise NotImplementedError("Subclass must implement this method")

    def query_media_url(self, media_id):
        raise NotImplementedError("Subclass must implement this method")

    def get_location(self, data):
        raise NotImplementedError("Subclass must implement this method")

    def get_image(self, data):
        raise NotImplementedError("Subclass must implement this method")

    def get_video(self, data):
        raise NotImplementedError("Subclass must implement this method")

    def download_media(self, media_url, mime_type):
        raise NotImplementedError("Subclass must implement this method")

    def get_audio(self, data):
        raise NotImplementedError("Subclass must implement this method")

    def get_document(self, data):
        raise NotImplementedError("Subclass must implement this method")

    def upload_media(self, media_name):
        raise NotImplementedError("Subclass must implement this method")

    def send_audio(self, audio_id, mobile, link):
        raise NotImplementedError("Subclass must implement this method")

    def send_contacts(self, contacts, notify_number):
        raise NotImplementedError("Subclass must implement this method")
