import logging
import threading
from queue import PriorityQueue


class PriorityQueueWithCounter:
    def __init__(self):
        self.pq = PriorityQueue()
        self.entry_counter = 0
        self.message_processing_event = threading.Event()

    def put(self, item, priority):
        # entry_counter serves to order items with the same priority
        # (also ensures that two items with the same priority won't attempt to sort by comparing the values)
        logging.debug(f"Put message in PriorityQueue - message:{item} - priority {priority}")
        entry = [priority, self.entry_counter, item]
        self.pq.put(entry)
        self.entry_counter += 1
        self.message_processing_event.set()

    def get(self):
        return self.pq.get()[-1]

    def empty(self):
        return self.pq.empty()
