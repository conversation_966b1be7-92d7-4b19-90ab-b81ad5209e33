# coding=utf8
import re
import logging
import datetime
from bson import ObjectId
import pytz
from sendgrid import SendGridAPIClient
from sendgrid.helpers.mail import Mail
from dotenv import load_dotenv
from config.config import get_config_instance

load_dotenv()

TIMEZONE = pytz.timezone('America/Argentina/Buenos_Aires')


def extract_regex(text, regex):
    try:
        matches = re.finditer(regex, text, re.MULTILINE | re.IGNORECASE)
        for matchNum, match in enumerate(matches, start=1):
            return match.group()
    except Exception as e:
        logging.error("Error al extraer el texto con la regex: %s", str(e))
        return None


def get_current_time():
    return datetime.datetime.now(pytz.utc).astimezone(TIMEZONE)


def send_email(email, conversation):
    conversation_html = ""

    # Acceder a los atributos de la instancia de la clase Conversation
    chathistory = conversation.chathistory
    lead = conversation.lead
    summary = conversation.summary

    # Iterar a través de la chathistory
    for entry in chathistory:
        role = entry.role
        content = entry.content

        # Agregar cada entrada al contenido HTML de la conversación
        if role == "user":
            conversation_html += f"<b>Usuario:</b> {content}<br><br>"
        elif role == "assistant":
            conversation_html += f"<b>Asistente:</b> {content}<br><br>"

    # Agregar la conversación al contenido HTML del correo
    message = Mail(
        from_email="<EMAIL>",
        to_emails=email,
        subject="Nuevo lead",
        html_content=f'Hola! Se acaba de generar un nuevo lead:<br><br>Nombre: {lead.fullname} <br>Telefono: {lead.phone} <br>Email: {lead.email} <br>Resumen: {summary} <br><b>Conversación:</b><br>{conversation_html}',
    )

    try:
        sg = SendGridAPIClient(get_config_instance().sendgrid_api_key)
        sg.send(message)
    except Exception as e:
        logging.error("Error al enviar email: %s", str(e))

def get_user_to_assign(client_id):
    from main import users_db, conversations_db
    try:
        # Obtener los usuarios activos del cliente
        client_users = users_db.get_documents_by_filter(
            {"clientId": str(client_id), "role": "user", "active": True}, {"_id": 1}
        )
        if not client_users:
            logging.warning(f"No active users found for client: {client_id}")
            return None

        # Obtener la última conversación asignada del cliente
        last_conversation = conversations_db.get_documents_by_filter(
            {"clientid": str(client_id), "assign": {"$exists": True}},
            {"assign": 1},
            sortField="createdAt",
            sortValue=-1,
            limit=1,
        )
        
        if last_conversation:
            last_user_assign_id = last_conversation[0]["assign"]
            last_user_assign_id_obj = ObjectId(last_user_assign_id)
        else:
            last_user_assign_id_obj = None

        # Ordenar usuarios para asignar al siguiente en la lista
        user_ids = [user["_id"] for user in client_users]

        if last_user_assign_id_obj and last_user_assign_id_obj in user_ids:
            last_index = user_ids.index(last_user_assign_id_obj)
            next_index = (last_index + 1) % len(user_ids)
        else:
            next_index = 0
        
        return str(user_ids[next_index])
    
    except Exception as e:
        logging.error(f"Error getting next user id: {str(e)}")
        return None