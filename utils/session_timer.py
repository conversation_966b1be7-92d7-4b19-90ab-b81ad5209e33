"""
SessionTimer - Singleton module for secure in-memory session management.
Provides thread-safe storage for session data with automatic expiration.
"""
import threading
from typing import Dict, Any, Optional, Callable, Tuple
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class SessionTimer:
    """
    Thread-safe singleton class for managing in-memory sessions.
    
    This class provides:
    - Session data storage with expiration
    - Timers for scheduled actions
    - Thread-safe synchronization
    - Automatic cleanup of expired sessions
    """
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(SessionTimer, cls).__new__(cls)
                cls._instance._initialize()
            return cls._instance
    
    def _initialize(self):
        """Initialize internal data structures."""
        self._sessions: Dict[str, Dict[str, Any]] = {}
        self._session_lock = threading.RLock()
        self._cleanup_timer = None
        logger.info("SessionTimer initialized")
        
    def setup_cleanup_timer(self, interval_seconds: int = 3600) -> None:
        """
        Set up a periodic timer to clean up expired sessions.
        
        Args:
            interval_seconds: Interval in seconds between cleanup runs (default: 1 hour)
        """
        def cleanup():
            try:
                self.cleanup_expired_sessions()
            except Exception as e:
                logger.error(f"Error during session cleanup: {e}", exc_info=True)
            finally:
                # Reschedule the cleanup
                if hasattr(self, '_cleanup_timer'):
                    self._cleanup_timer = threading.Timer(interval_seconds, cleanup)
                    self._cleanup_timer.daemon = True
                    self._cleanup_timer.start()
        
        # Cancel any existing timer
        if hasattr(self, '_cleanup_timer') and self._cleanup_timer:
            self._cleanup_timer.cancel()
            
        # Start the cleanup timer
        self._cleanup_timer = threading.Timer(interval_seconds, cleanup)
        self._cleanup_timer.daemon = True
        self._cleanup_timer.start()
        logger.info(f"Session cleanup timer started with {interval_seconds}s interval")
        
    def __del__(self):
        """Clean up resources when the instance is destroyed."""
        if hasattr(self, '_cleanup_timer') and self._cleanup_timer:
            self._cleanup_timer.cancel()
    
    def create_session(self, session_id: str, ttl_seconds: int = 3600, **kwargs) -> None:
        """
        Create a new session with a specific time-to-live.
        
        Args:
            session_id: Unique session identifier
            ttl_seconds: Time to live in seconds (default: 1 hour)
            **kwargs: Additional data to store in the session
        """
        with self._session_lock:
            if session_id in self._sessions:
                logger.warning(f"Session {session_id} already exists, updating")
                self._cleanup_session(session_id)
                
            self._sessions[session_id] = {
                'data': kwargs,
                'expires_at': datetime.now() + timedelta(seconds=ttl_seconds),
                'timer': None,
                'created_at': datetime.now(),
                'last_accessed': datetime.now()
            }
            logger.debug(f"Session created: {session_id}")
    
    def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Get session data if it exists and hasn't expired.
        
        Args:
            session_id: Session identifier
            
        Returns:
            Dict with session data or None if it doesn't exist or has expired
        """
        with self._session_lock:
            session = self._sessions.get(session_id)
            if not session:
                return None
                
            if datetime.now() > session['expires_at']:
                logger.debug(f"Session expired: {session_id}")
                self._cleanup_session(session_id)
                return None
                
            # Actualizar último acceso
            session['last_accessed'] = datetime.now()
            return session['data']
    
    def update_session_ttl(self, session_id: str, ttl_seconds: int) -> bool:
        """
        Update the time-to-live of an existing session.
        
        Args:
            session_id: Session identifier
            ttl_seconds: New time-to-live in seconds
            
        Returns:
            bool: True if updated, False if session doesn't exist
        """
        with self._session_lock:
            if session_id not in self._sessions:
                return False
                
            self._sessions[session_id]['expires_at'] = (
                datetime.now() + timedelta(seconds=ttl_seconds)
            )
            self._sessions[session_id]['last_accessed'] = datetime.now()
            logger.debug(f"TTL updated for session {session_id}")
            return True
    
    def delete_session(self, session_id: str) -> bool:
        """
        Delete a specific session.
        
        Args:
            session_id: Identifier of the session to delete
            
        Returns:
            bool: True if deleted, False if it didn't exist
        """
        with self._session_lock:
            if session_id not in self._sessions:
                return False
                
            self._cleanup_session(session_id)
            logger.info(f"Session deleted: {session_id}")
            return True
    
    def set_timer(
        self, 
        session_id: str, 
        timeout: int, 
        callback: Callable,
        *args,
        **kwargs
    ) -> bool:
        """
        Set a timer for a session.
        
        Args:
            session_id: Session identifier
            timeout: Time in seconds before the callback is executed
            callback: Function to execute when the timer expires
            *args, **kwargs: Arguments for the callback
            
        Returns:
            bool: True if timer was set, False if session doesn't exist
        """
        with self._session_lock:
            if session_id not in self._sessions:
                logger.warning(f"Could not set timer: session {session_id} not found")
                return False
                
            # Cancelar temporizador existente si lo hay
            if self._sessions[session_id]['timer']:
                self._sessions[session_id]['timer'].cancel()
            
            # Crear nuevo temporizador
            timer = threading.Timer(
                timeout, 
                self._timer_callback, 
                args=(session_id, callback, args, kwargs)
            )
            timer.daemon = True
            self._sessions[session_id]['timer'] = timer
            timer.start()
            logger.debug(f"Timer set for session {session_id} (timeout: {timeout}s)")
            return True
    
    def _timer_callback(
        self, 
        session_id: str, 
        callback: Callable, 
        args: Tuple, 
        kwargs: Dict[str, Any]
    ) -> None:
        """Timer callback handler."""
        try:
            logger.debug(f"Executing callback for session {session_id}")
            callback(*args, **kwargs)
        except Exception as e:
            logger.error(f"Error in timer callback: {e}", exc_info=True)
        finally:
            # Don't automatically delete session, let the callback decide
            pass
    
    def _cleanup_session(self, session_id: str) -> None:
        """
        Clean up session resources.
        
        Args:
            session_id: Identifier of the session to clean up
        """
        if session_id in self._sessions:
            session = self._sessions.pop(session_id)
            if session['timer']:
                try:
                    session['timer'].cancel()
                except Exception as e:
                    logger.debug(f"Error cancelling timer for session {session_id}: {e}")
            logger.debug(f"Resources freed for session: {session_id}")
    
    def cleanup_expired_sessions(self) -> int:
        """
        Clean up all expired sessions.
        
        Returns:
            int: Number of sessions cleaned up
        """
        count = 0
        with self._session_lock:
            now = datetime.now()
            expired = [
                sid for sid, sess in self._sessions.items() 
                if now > sess['expires_at']
            ]
            for sid in expired:
                self._cleanup_session(sid)
                count += 1
                
        if count > 0:
            logger.info(f"Limpieza completada: {count} sesiones expiradas eliminadas")
        return count
    
    def get_all_sessions(self) -> Dict[str, Dict[str, Any]]:
        """
        Obtiene información de todas las sesiones activas.
        
        Returns:
            Dict con información de las sesiones
        """
        with self._session_lock:
            return {
                sid: {
                    'created_at': sess['created_at'],
                    'last_accessed': sess['last_accessed'],
                    'expires_at': sess['expires_at'],
                    'has_timer': sess['timer'] is not None,
                    'data_keys': list(sess['data'].keys()) if sess['data'] else []
                }
                for sid, sess in self._sessions.items()
            }
