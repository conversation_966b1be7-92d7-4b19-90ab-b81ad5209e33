import logging
from flask import jsonify
from db.client import Client


class ClientService:
    def __init__(self, db):
        self.db = db

    def get_client_information(self, client_id):
        try:
            document = self.db.get_document(client_id)
            if document:
                # Convertir ObjectId a cadena
                document["_id"] = str(document["_id"])
                return document
            else:
                return None
        except Exception as e:
            logging.error(f"Error al obtener la información del cliente {client_id}: {str(e)}")
            raise

    def add_client_information(self, req_data):
        try:
            client_dict = Client.build_to_json(req_data)
            logging.debug(f"Save document in DB: {client_dict}")
            self.db.insert_document(client_dict)
            return jsonify({"message": "Cliente agregado exitosamente", "status": 200})
        except Exception as e:
            logging.error(f"Error al agregar el agregar la informacion del cliente: {str(e)}")
            return jsonify({"error": "Ocurrió un error para agregar la informacion del cliente"}), 500
