import json


class CacheService:
    def __init__(self, redis_db):
        self.redis_db = redis_db

    def delete_cache_value(self, key: str):
        """
        Borra un valor en la caché con la clave especificada.
        """
        self.redis_db.delete_key(key)

    def set_cache_value(self, key: str, thread_id: str, conversation_id: str, token_usage: int, update_conversation: bool):
        """
        Serializa y guarda un valor en la caché con la clave especificada.
        """
        cache_value = {
            "thread_id": thread_id,
            "conversation_id": conversation_id,
            "token_usage": token_usage,
            "update_conversation": update_conversation
        }
        self.redis_db.set_key_value(key, json.dumps(cache_value))

    def get_cache_value(self, key: str):
        """
        Recupera y deserializa el valor almacenado en la caché para la clave especificada.
        """
        value = self.redis_db.get_value(key)
        if value:
            json_string = value.replace("'", "\"")
            return json.loads(json_string)
        return None
