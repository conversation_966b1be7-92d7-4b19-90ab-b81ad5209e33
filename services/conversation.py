import logging
from typing import Optional

from bson import ObjectId
from db.models import Conversation
from dtos.conversation_dto import ConversationDTO
from utils.utils import get_current_time

class ConversationService:
    def __init__(self, db, cache):
        self.db = db
        self.cache = cache

    def get_conversation_by_hash(self, lead_hash) -> Optional[ConversationDTO]:
        try:
            documents = self.db.get_documents_by_filter(
                query={
                    "lead.hash": lead_hash
                },
                proyections={},
                sortField="createdAt",
                sortValue=-1,
                limit=1,
            )
            if documents:
                return ConversationDTO(documents[0])
            else:
                return None
        except Exception as e:
            logging.error(f"Error getting conversation with lead hash {lead_hash}: {str(e)}")
            raise

    def insert_conversation(self, conversation: Conversation):
        try:
            conversation_dict = conversation.to_mongo().to_dict()

            if not conversation_dict.get("_id"):
                conversation_dict.pop("_id", None)

            logging.debug(f"Save document in DB: {conversation_dict}")
            conversation_id = self.db.insert_document(conversation_dict)
            return conversation_id
        except Exception as e:
            logging.error(f"Error insert conversation in db: {str(e)}")
            raise

    def update_chat_history_in_conversation(self, conversation_id, new_message):
        try:
            current_datetime = get_current_time()
            self.db.update_many(
                {
                    "_id": ObjectId(conversation_id),
                },
                {
                    "$push": {"chathistory": new_message},
                    "$set": {"updatedAt": current_datetime}
                }
            )
        except Exception as e:
            logging.error(f"Error al actualizar la conversación {conversation_id}: {str(e)}")
            raise

    def update_conversations_with_contact(self, conversation_id, token_usage, all_messages):
        try:
            current_datetime = get_current_time()
            self.db.update_many(
                {"_id": ObjectId(conversation_id)},
                {"$set": {"chathistory": all_messages, "tokenusage": token_usage, "active": True, "status": "nuevo", "botstop": True, "updatedAt": current_datetime}}
            )
        except Exception as e:
            logging.error(f"Error update conversation {conversation_id}: {str(e)}")
            raise

    def exist_conversation_hash_in_cache(self, conversation_hash):
        try:
            cache_data_str = self.cache.get_value(conversation_hash)
            if cache_data_str is None:
                return False
            else:
                return True
        except Exception as e:
            logging.error(
                f"Error al obtener la información en la cache de la conversation con el hash {conversation_hash}: {str(e)}")
            raise
