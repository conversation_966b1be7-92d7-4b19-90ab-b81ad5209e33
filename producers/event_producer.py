import json
import pika
import uuid

from datetime import datetime
from utils.rabbitmq_connection import get_channel

def send_event(event_type: str, payload: dict):
    channel, connection = get_channel()
    channel.queue_declare(queue=event_type, durable=True)
    message = dict(payload)  # Copia para no mutar el original
    message["timestamp"] = datetime.now().isoformat()
    message["messageId"] = str(uuid.uuid4())
    channel.basic_publish(
        exchange='',
        routing_key=event_type,
        body=json.dumps(payload),
        properties=pika.BasicProperties(delivery_mode=2)  # make message persistent
    )
    connection.close()
