import logging

import requests
from dotenv import load_dotenv
from flask import Flask, request, jsonify
from flask_cors import CORS
from flask_executor import Executor
from prometheus_client import CONTENT_TYPE_LATEST
from prometheus_client import generate_latest

from ai.chatgpt import get_chatgpt_response
from broker.broker import Queue<PERSON>anager
from cache.cache import RedisDatabase
from config.config import get_config_instance
from connectors.connector_factory import ConnectorFactory
from consumers.consumer_manager import start_all_consumers
from consumers.received_crm_message_consumer import start_received_crm_message_consumer
from consumers.received_superchats_message_consumer import start_received_superchats_message_consumer
from db.models import Conversation
from db.mongo import MongoDatabase
from enums.connector_types import ConnectorType
from metrics import REQUEST_COUNT, REQUEST_LATENCY
from services.cache import CacheService
from services.client import ClientService
from services.conversation import ConversationService
from utils.utils import send_email

# Initialize Flask App
app = Flask(__name__)
CORS(app)

logging.basicConfig(
    filename='api.log',
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

load_dotenv()

config = get_config_instance()

# Instanciar las bases de datos
cache = RedisDatabase(config.redis_host, config.redis_port, config.redis_username, config.redis_password, 0)
conversations_db = MongoDatabase(config.mongo_uri, config.mongo_user, config.mongo_password, config.mongo_host,
                                 config.mongo_db, 'conversations')
clients_db = MongoDatabase(config.mongo_uri, config.mongo_user, config.mongo_password, config.mongo_host,
                           config.mongo_db, 'clients')
users_db = MongoDatabase(config.mongo_uri, config.mongo_user, config.mongo_password, config.mongo_host,
                           config.mongo_db, 'users')

# Services
client_service = ClientService(clients_db)
conversations_service = ConversationService(conversations_db, cache)
cache_service = CacheService(cache)

# Connectors
ConnectorFactory.initialize(conversations_service, cache_service, client_service)
connectors = ConnectorFactory.get_all_connectors()

# QueueManager
queueManager = QueueManager(client_service, connectors)

# Start Consumers
consumers = [
    (start_received_crm_message_consumer, (conversations_service,)),
    (start_received_superchats_message_consumer, ())
]

start_all_consumers(consumer_specs=consumers)

# Define routes
# TODO move to routes.py file

@app.get("/backend/wpp")
def wpp_verify_token():
    return connectors[ConnectorType.WHATSAPP_API].verify_token()


@app.post("/backend/wpp")
@REQUEST_LATENCY.time()
def wpp_hook():
    REQUEST_COUNT.labels(method='POST', endpoint='/backend/wpp').inc()
    req_data = request.get_json(force=True)
    logging.info("Webhook Whatsapp data: %s", req_data)
    executor.submit(queueManager.hook(req_data, ConnectorType.WHATSAPP_API))

    return "OK", 200


@app.post("/backend/venomWpp")
@REQUEST_LATENCY.time()
def wpp_venom_hook():
    REQUEST_COUNT.labels(method='POST', endpoint='/backend/venomWpp').inc()
    req_data = request.get_json(force=True)
    logging.info("Webhook Venom Whatsapp data: %s", req_data)
    executor.submit(queueManager.hook(req_data, ConnectorType.WHATSAPP_VENOM))

    return "OK", 200

@app.get("/backend/fb")
def fb_verify_token():
    return connectors[ConnectorType.FACEBOOK].verify_token()


@app.post("/backend/fb")
def fb_hook():
    req_data = request.get_json(force=True)
    logging.info("Webhook Whatsapp data: %s", req_data)
    executor.submit(queueManager.hook(req_data, ConnectorType.FACEBOOK))

    return "OK", 200


@app.post("/backend/q&a")
def askGpt():
    req_data = request.get_json(force=True)
    questions = req_data['questions']
    response_data = get_chatgpt_response(questions=questions, json_response=False)
    return jsonify(response_data)


@app.post("/backend/lead")
def addLead():
    try:
        req_data = request.get_json(force=True)
        conversation_dict = Conversation.build_to_json(req_data)
        logging.debug(f"Save document in DB: {conversation_dict}")
        conversations_db.insert_document(conversation_dict)
        if req_data['emailNotificate']:
            send_email(req_data['emailNotificate'], conversation_dict)
        payload = {"clientId": conversation_dict["clientid"], "status": conversation_dict["status"]}
        requests.post(config.url_crm + '/api/newLead', json=payload)
        return jsonify({"message": "Lead agregado exitosamente", "status": 200})
    except Exception as e:
        logging.error(f"Error al agregar el lead: {str(e)}")
        return jsonify({"error": "Ocurrió un error al agregar el lead"}), 500


@app.get("/backend/client/info")
def getClientInformation():
    client_id = request.args.get("client.id")
    try:
        client_info = client_service.get_client_information(client_id)
        if client_info is not None:
            return jsonify(client_info), 200
        else:
            return jsonify({"error": "El cliente solicitado no existe"}), 404
    except Exception as e:
        return jsonify({"error": "Ocurrió un error al obtener la información del cliente", "details": str(e)}), 500


@app.post("/backend/client/info")
def addClientInformation():
    req_data = request.get_json(force=True)
    return client_service.add_client_information(req_data)


@app.get("/backend/conversation/exist")
def getConversationExistInCache():
    conversation_hash = request.args.get("conversation.hash")
    try:
        response = conversations_service.exist_conversation_hash_in_cache(conversation_hash)
        return {"exist": response}, 200
    except Exception as e:
        return jsonify(
            {"error": "Error al obtener la información en la cache de la conversation", "details": str(e)}), 500


@app.route("/metrics")
def metrics():
    """
    Endpoint para que Prometheus recoja las métricas.
    """
    return generate_latest(), 200, {'Content-Type': CONTENT_TYPE_LATEST}


if __name__ == "__main__":
    executor = Executor(app)
    app.run(port=5000, debug=False)
