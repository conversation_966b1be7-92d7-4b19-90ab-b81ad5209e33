import unittest
from unittest.mock import MagicMock, patch, ANY
from lead.integrations.integration_manager import IntegrationManager

class TestIntegrationManager(unittest.TestCase):
    def setUp(self):
        self.manager = IntegrationManager()
        self.mock_client_data = MagicMock()
        
    def test_build_dto_with_known_integration(self):
        """Test that _build_dto returns a DTO for a known integration"""
        # This test will verify that the _build_dto method returns a DTO
        # for a known integration (mailchimp in this case)
        dto = self.manager._build_dto("mailchimp", self.mock_client_data)
        self.assertIsNotNone(dto)
        self.assertEqual(dto.__class__.__name__, "MailchimpDTO")
        
    def test_build_dto_with_unknown_integration(self):
        """Test that _build_dto returns None for an unknown integration"""
        with patch('logging.warning') as mock_warning:
            dto = self.manager._build_dto("unknown_integration", self.mock_client_data)
            self.assertIsNone(dto)
            mock_warning.assert_called_once_with(
                "No hay DTO definido para la integración: unknown_integration"
            )
    
    def test_process_integrations_with_known_integration(self):
        """Test processing integrations with a known integration"""
        # Create a mock integration class
        mock_integration = MagicMock()
        mock_integration_instance = MagicMock()
        mock_integration.return_value = mock_integration_instance
        
        # Create a mock DTO
        mock_dto = MagicMock()
        
        # Patch the integration classes dictionary
        with patch.object(self.manager, 'integration_classes', {"test_integration": mock_integration}), \
             patch.object(self.manager, 'dto_classes', {"test_integration": MagicMock(return_value=mock_dto)}):
            
            # Call the method under test
            self.manager.process_integrations(["test_integration"], self.mock_client_data)
            
            # Verify the integration was processed correctly
            mock_integration.assert_called_once()
            mock_integration_instance.execute.assert_called_once_with(mock_dto)
    
    def test_process_integrations_with_unknown_integration(self):
        """Test processing integrations with an unknown integration"""
        with patch('logging.warning') as mock_warning:
            # Call with an unknown integration
            self.manager.process_integrations(["unknown_integration"], self.mock_client_data)
            
            # Verify the warning was logged
            mock_warning.assert_called_once_with(
                "No hay integración definida para: unknown_integration"
            )
    
    def test_process_integrations_with_multiple_integrations(self):
        """Test processing multiple integrations at once"""
        # Create mock integrations
        mock_integration1 = MagicMock()
        mock_integration_instance1 = MagicMock()
        mock_integration1.return_value = mock_integration_instance1
        
        mock_integration2 = MagicMock()
        mock_integration_instance2 = MagicMock()
        mock_integration2.return_value = mock_integration_instance2
        
        # Patch the integration classes dictionary
        with patch.object(self.manager, 'integration_classes', {
            "test1": mock_integration1,
            "test2": mock_integration2
        }), patch.object(self.manager, 'dto_classes', {
            "test1": MagicMock(return_value=MagicMock()),
            "test2": MagicMock(return_value=MagicMock())
        }):
            # Call with multiple integrations
            self.manager.process_integrations(["test1", "test2"], self.mock_client_data)
            
            # Verify both integrations were processed
            mock_integration1.assert_called_once()
            mock_integration2.assert_called_once()
            mock_integration_instance1.execute.assert_called_once()
            mock_integration_instance2.execute.assert_called_once()

if __name__ == '__main__':
    unittest.main()
