import unittest
from unittest.mock import MagicMock, patch

# Mock MongoDB connection before importing anything that might use it
import mongomock
from mongoengine import connect

# Set up the MongoDB mock connection
connect('mongoenginetest', mongo_client_class=mongomock.MongoClient)

from lead.lead_manager import Lead<PERSON>anager, MILKAUT_CLIENT_ID
from db.models import Lead


class TestLeadManager(unittest.TestCase):
    def setUp(self):
        # Create mock objects
        self.mock_conversations = {
            'test_hash': {
                'thread_id': 'test_thread_id',
                'token_usage': 100,
                'conversation_id': 'test_conversation_id'
            }
        }
        self.mock_conversations_service = MagicMock()
        self.mock_connector = MagicMock()
        self.mock_message_factory = MagicMock()
        self.mock_cache_service = MagicMock()

        # Configure message factory
        self.mock_message_factory.hash = 'test_hash'
        self.mock_message_factory.mobile = '+1234567890'
        self.mock_message_factory.name = 'Test User'
        self.mock_message_factory.client_id = 'test_client_id'
        self.mock_message_factory.notify_number = None
        self.mock_message_factory.notificate = False
        self.mock_message_factory.client_email = None
        self.mock_message_factory.notificate_to_email = False
        self.mock_message_factory.wa_id = '1234567890'
        self.mock_message_factory.rules = {}
        self.mock_message_factory.assistant_id = 'test_assistant_id'
        self.mock_message_factory.before_instruction_prompt = ''
        self.mock_message_factory.after_instruction_prompt = ''
        self.mock_message_factory.finish_conversation_vars = {}
        self.mock_message_factory.integrations = []

        # Create LeadManager instance
        self.lead_manager = LeadManager(
            conversations=self.mock_conversations,
            conversations_service=self.mock_conversations_service,
            connector=self.mock_connector,
            message_factory=self.mock_message_factory,
            cache_service=self.mock_cache_service
        )
        self.lead_manager.conversation_id = 'test_conversation_id'

    @patch('lead.lead_manager.send_email')
    @patch('lead.lead_manager.LeadManager.notificate_lead')
    @patch('lead.lead_manager.apply_regex')
    @patch('lead.lead_manager.validate_rules')
    @patch('lead.lead_manager.LeadManager.get_final_conversation')
    @patch('lead.lead_manager.LeadManager.save_conversation')
    @patch('lead.lead_manager.send_event')
    @patch('lead.lead_manager.GENERATE_LEAD_COUNT')
    @patch('lead.lead_manager.GENERATE_LEAD_LATENCY')
    def test_generate_lead_success(self, mock_latency, mock_lead_count, mock_send_event, 
                                 mock_save_conversation, mock_get_final_conversation, 
                                 mock_validate_rules, mock_apply_regex, 
                                 mock_notificate_lead, mock_send_email):
        # Configurar notificaciones para probar las líneas 86 y 88
        self.lead_manager.notificate = True
        self.lead_manager.notificate_to_email = True
        self.lead_manager.client_email = '<EMAIL>'
        
        # Configurar el thread_id en el diccionario de conversaciones
        thread_id = 'test_thread_id'
        self.lead_manager.conversations[self.lead_manager.hash] = {
            'thread_id': thread_id,
            'conversation_id': 'test_conversation_id'
        }
        
        # Configurar los mocks
        mock_assistant = MagicMock()
        mock_messages = [
            {'role': 'user', 'content': 'Hello'},
            {'role': 'assistant', 'content': 'Hi there!'}
        ]
        mock_assistant.get_messages_in_thread_formatted.return_value = mock_messages
        
        # Mock de AssistantManager
        with patch('lead.lead_manager.AssistantManager.get_assistant_by_id') as mock_get_assistant:
            mock_get_assistant.return_value = mock_assistant
            
            # Configurar el mock para apply_regex
            mock_apply_regex.return_value = {'name': 'Test User', 'email': '<EMAIL>'}
            mock_validate_rules.return_value = True
            
            # Crear un mock para la conversación
            mock_conversation = MagicMock()
            mock_conversation.lead = Lead(fullname='Test User', email='<EMAIL>', 
                                       phone='+1234567890', hash='test_hash')
            mock_conversation.additional_fields = {}
            mock_conversation.summary = 'Test summary'
            mock_get_final_conversation.return_value = mock_conversation
            
            # Configurar los mocks para las métricas
            mock_latency.time.return_value.__enter__.return_value = None
            mock_lead_count.inc = MagicMock()  # Asegurarse de que inc() sea un mock
            
            # Llamar al método a través de generate_or_update_lead para asegurar que se incrementa el contador
            self.lead_manager.generate_or_update_lead(update_conversation=False)
            
            # Verificaciones
            # Verificar que se llamó a get_messages_in_thread_formatted con el thread_id correcto
            mock_assistant.get_messages_in_thread_formatted.assert_called_once_with(thread_id=thread_id)
            
            # Verificar que se llamó a apply_regex con los parámetros correctos
            mock_apply_regex.assert_called_once_with(
                self.lead_manager.rules,
                mock_messages
            )
            
            # Verificar que se llamó a validate_rules con el resultado de apply_regex
            mock_validate_rules.assert_called_once_with(
                self.lead_manager.rules,
                mock_apply_regex.return_value
            )
            
            # Verificar que se llamó a get_final_conversation
            mock_get_final_conversation.assert_called_once_with(
                all_messages=mock_messages,
                finish_conversation_json=self.lead_manager.finish_conversation_vars
            )
            
            # Verificar que se llamó a save_conversation
            mock_save_conversation.assert_called_once_with(mock_conversation, thread_id)
            
            # Verificar que se llamó a send_event
            mock_send_event.assert_called_once()
            
            # Verificar que se llamó a notificate_lead
            mock_notificate_lead.assert_called_once_with(self.lead_manager.mobile, mock_conversation)
            
            # Verificar que se llamó a send_email
            mock_send_email.assert_called_once_with(self.lead_manager.client_email, mock_conversation)
        mock_validate_rules.assert_called_once_with(
            self.mock_message_factory.rules, 
            mock_apply_regex.return_value
        )
        mock_get_final_conversation.assert_called_once()
        mock_save_conversation.assert_called_once_with(mock_conversation, 'test_thread_id')
        mock_send_event.assert_called_once()
        
        # Verify metrics were called
        mock_lead_count.inc.assert_called_once()

    @patch('lead.lead_manager.AssistantManager')
    @patch('lead.lead_manager.apply_regex')
    @patch('lead.lead_manager.validate_rules')
    @patch('lead.lead_manager.LeadManager.save_conversation')
    def test_generate_lead_no_contact(self, mock_save_conversation, mock_validate_rules, 
                                    mock_apply_regex, mock_assistant_manager):
        # Setup mocks
        mock_assistant = MagicMock()
        mock_assistant.get_messages_in_thread_formatted.return_value = [
            {'role': 'user', 'content': 'Hello'},
            {'role': 'assistant', 'content': 'Hi there!'}
        ]
        mock_assistant_manager.get_assistant_by_id.return_value = mock_assistant
        
        mock_apply_regex.return_value = {'name': 'Test User'}
        mock_validate_rules.return_value = False  # No contact
        
        # Create a mock for Conversation with proper attributes
        mock_conversation = MagicMock()
        mock_conversation.lead = Lead(fullname='Test User', phone='+1234567890', hash='test_hash')
        mock_conversation.additional_fields = {}
        
        # Patch get_final_conversation to return our mock
        with patch('lead.lead_manager.LeadManager.get_final_conversation', return_value=mock_conversation) as mock_get_final_conversation:
            # Call the method
            self.lead_manager.generate_lead()
            
            # Assertions
            mock_get_final_conversation.assert_called_once()
            # Verify the conversation was updated with no contact status
            self.assertEqual(mock_conversation.status, 'sincontacto')
            self.assertFalse(mock_conversation.active)
            # Verify the conversation was saved
            mock_save_conversation.assert_called_once_with(mock_conversation, 'test_thread_id')

    @patch('lead.lead_manager.get_chatgpt_response')
    @patch('lead.lead_manager.LeadManager.build_json_conversation')
    def test_get_final_conversation(self, mock_build_json_conversation, mock_get_chatgpt_response):
        # Setup test data
        all_messages = [
            {'role': 'system', 'content': 'Welcome'},
            {'role': 'user', 'content': 'Hello'},
            {'role': 'assistant', 'content': 'Hi there!'}
        ]
        finish_conversation_json = '{"name": "Test User"}'
        
        # Mock the response from get_chatgpt_response
        mock_response = {
            'message': '{"name": "Test User", "email": "<EMAIL>"}'
        }
        mock_get_chatgpt_response.return_value = mock_response
        
        # Mock the build_json_conversation method
        expected_conversation = MagicMock()
        mock_build_json_conversation.return_value = expected_conversation
        
        # Call the method
        result = self.lead_manager.get_final_conversation(all_messages, finish_conversation_json)
        
        # Assertions
        self.assertEqual(result, expected_conversation)
        mock_get_chatgpt_response.assert_called_once()
        
        # Verify the messages were processed correctly
        messages_arg = mock_get_chatgpt_response.call_args[1]['questions']
        self.assertEqual(len(messages_arg), 4)  # Original messages + system message
        self.assertEqual(messages_arg[-1]['role'], 'system')
        
        # Verify build_json_conversation was called with the right arguments
        mock_build_json_conversation.assert_called_once()
        args, kwargs = mock_build_json_conversation.call_args
        self.assertEqual(args[0], {'name': 'Test User', 'email': '<EMAIL>'})  # conversation_vars
        
        # Verify the messages passed to build_json_conversation
        # The method might be filtering or transforming the messages, so we'll just check the type
        self.assertIsInstance(args[1], list)  # Should be a list of messages

    def test_extract_tags(self):
        # Test data - the actual implementation expects tags in a 'tags' dictionary
        additional_fields = {
            'tags': {
                'new_customer': 'true',
                'vip': 'false',
                'interested': 'True',
                'invalid': 'maybe',
                'no_tag': 'no'
            },
            'other_field': 'value'
        }
        
        # Call the method
        tags = self.lead_manager.extract_tags(additional_fields)
        
        # Assertions - the method should return a list of tag names that are 'true' or 'True'
        self.assertIn('new_customer', tags)
        self.assertIn('interested', tags)
        self.assertNotIn('vip', tags)  # Should be excluded because it's 'false'
        self.assertNotIn('invalid', tags)  # Should be excluded because it's not a valid boolean string
        self.assertNotIn('no_tag', tags)  # Should be excluded because it's 'no'
        self.assertIn('other_field', additional_fields)  # Should not be removed from additional_fields

    def test_notificate_lead(self):
        # Configurar el lead_manager
        self.lead_manager.notify_number = '1234567890'
        self.lead_manager.name = 'Test User'
        self.lead_manager.wa_id = 'whatsapp:+1234567890'
        
        # Crear un mock para la conversación
        mock_conversation = MagicMock()
        mock_conversation.lead = MagicMock()
        mock_conversation.lead.fullname = 'Test User'
        mock_conversation.lead.phone = '+1234567890'
        mock_conversation.summary = None  # Para probar la línea 197
        mock_conversation.additional_fields = {}
        
        # Configurar los mocks para los métodos del conector
        self.lead_manager.connector.send_message = MagicMock()
        self.lead_manager.connector.send_contacts = MagicMock()
        
        # Llamar al método
        self.lead_manager.notificate_lead('+1234567890', mock_conversation)
        
        # Verificar que se actualizó el resumen cuando era None
        self.assertEqual(mock_conversation.summary, "El cliente no dejo informacion")
        
        # Verificar que se llamó a send_message con los parámetros correctos
        self.lead_manager.connector.send_message.assert_called_once()
        
        # Obtener los argumentos con los que se llamó a send_message
        message_args, message_kwargs = self.lead_manager.connector.send_message.call_args
        
        # Verificar que se está enviando al número de notificación correcto
        self.assertEqual(len(message_args), 2)
        self.assertEqual(message_args[1], '1234567890')  # Número de notificación
        
        # Verificar que el mensaje contiene información relevante
        self.assertIn('Lead', message_args[0])
        self.assertIn('Resumen', message_args[0])
        
        # Verificar que se llamó a send_contacts con los contactos correctos
        self.lead_manager.connector.send_contacts.assert_called_once()
        
        # Obtener los argumentos con los que se llamó a send_contacts
        contacts_args, contacts_kwargs = self.lead_manager.connector.send_contacts.call_args
        
        # Verificar que se están enviando los contactos correctos
        self.assertEqual(len(contacts_args), 2)
        self.assertIsInstance(contacts_args[0], list)
        self.assertEqual(len(contacts_args[0]), 1)
        self.assertEqual(contacts_args[0][0]['name']['formatted_name'], 'Test User')
        self.assertEqual(contacts_args[0][0]['phones'][0]['phone'], '+1234567890')

    @patch('lead.lead_manager.IntegrationManager')
    def test_run_integrations(self, mock_integration_manager_class):
        # Setup mocks
        mock_integration_manager = MagicMock()
        mock_integration_manager_class.return_value = mock_integration_manager
        
        # Test data - use a simple mock instead of spec=Conversation to avoid DB issues
        conversation = MagicMock()
        self.lead_manager.integrations = [{'type': 'test', 'config': {}}]
        
        # Call the method
        self.lead_manager.run_integrations(conversation)
        
        # Assertions
        mock_integration_manager_class.assert_called_once()
        mock_integration_manager.process_integrations.assert_called_once_with(
            integrations=self.lead_manager.integrations,
            client_data=conversation
        )

    @patch('lead.lead_manager.get_user_to_assign')
    def test_build_json_conversation(self, mock_get_user_to_assign):
        # Importar el modelo Message aquí para evitar problemas de importación circular

        # Configurar mocks
        mock_get_user_to_assign.return_value = "test_user"
        conversation.lead.fullname = 'Test User'
        conversation.summary = 'Test summary'
        
        # Call the method
        self.lead_manager.notificate_lead('+1234567890', conversation)
        
        # Assertions
        self.mock_connector.send_message.assert_called_once()
        self.mock_connector.send_contacts.assert_called_once()
        
        # Check the contacts sent
        contacts_arg = self.mock_connector.send_contacts.call_args[0][0]
        self.assertEqual(contacts_arg[0]['name']['formatted_name'], 'Test User')
        self.assertEqual(contacts_arg[0]['phones'][0]['phone'], '+1234567890')

    @patch('lead.lead_manager.AssistantManager')
    @patch('lead.lead_manager.apply_regex')
    @patch('lead.lead_manager.validate_rules')
    @patch('lead.lead_manager.UPDATE_LEAD_COUNT')
    @patch('lead.lead_manager.GENERATE_LEAD_LATENCY')
    def test_update_lead_with_contact(self, mock_latency, mock_lead_count, mock_validate_rules, 
                                    mock_apply_regex, mock_assistant_manager):
        # Setup mocks
        mock_assistant = MagicMock()
        mock_assistant.get_messages_in_thread_formatted.return_value = [
            {'role': 'system', 'content': 'Welcome'},
            {'role': 'user', 'content': 'Hello'},
            {'role': 'assistant', 'content': 'Hi there!'}
        ]
        mock_assistant_manager.get_assistant_by_id.return_value = mock_assistant
        
        mock_apply_regex.return_value = {'name': 'Test User', 'email': '<EMAIL>'}
        mock_validate_rules.return_value = True  # Indica que hay contacto
        
        # Setup the latency context manager mock
        mock_latency.time.return_value.__enter__.return_value = None

        # Call the method through generate_or_update_lead to ensure metrics are updated
        self.lead_manager.generate_or_update_lead(update_conversation=True)

        # Assertions
        mock_assistant.get_messages_in_thread_formatted.assert_called_once_with(
            thread_id='test_thread_id'
        )
        
        # Verificar que se llamó al método correcto del servicio de conversaciones
        self.mock_conversations_service.update_conversations_with_contact.assert_called_once()
        
        # Verificar que NO se llamó al método de sin contacto
        self.mock_conversations_service.update_conversations_without_contact.assert_not_called()
        
        # Verificar que se actualizó el contador de métricas
        mock_lead_count.inc.assert_called_once()

    @patch('lead.lead_manager.AssistantManager')
    @patch('lead.lead_manager.apply_regex')
    @patch('lead.lead_manager.validate_rules')
    def test_update_lead_without_contact(self, mock_validate_rules, 
                                       mock_apply_regex, mock_assistant_manager):
        # Setup mocks
        mock_assistant = MagicMock()
        mock_assistant.get_messages_in_thread_formatted.return_value = [
            {'role': 'system', 'content': 'Welcome'},
            {'role': 'user', 'content': 'Hello'},
            {'role': 'assistant', 'content': 'Hi there!'}
        ]
        mock_assistant_manager.get_assistant_by_id.return_value = mock_assistant
        
        mock_apply_regex.return_value = {'name': 'Test User'}
        mock_validate_rules.return_value = False  # Indica que NO hay contacto
        
        # Call the method directly
        self.lead_manager.update_lead()
        
        # Verificar que se llamó al método correcto del servicio de conversaciones
        self.mock_conversations_service.update_conversations_without_contact.assert_called_once()
        
        # Verificar que NO se llamó al método con contacto
        self.mock_conversations_service.update_conversations_with_contact.assert_not_called()
        
        # Verificar que se eliminó el primer mensaje (bienvenida)
        messages_arg = mock_assistant.get_messages_in_thread_formatted.return_value
        self.assertEqual(len(messages_arg), 2)  # Debería haber eliminado el mensaje de bienvenida

    @patch('lead.lead_manager.AssistantManager')
    @patch('lead.lead_manager.apply_regex')
    @patch('lead.lead_manager.validate_rules')
    @patch('lead.lead_manager.LeadManager.run_integrations')
    @patch('lead.lead_manager.send_event')
    def test_generate_lead_milkaut_client(self, mock_send_event, mock_run_integrations,
                                         mock_validate_rules, mock_apply_regex, mock_assistant_manager):
        # Setup mocks
        mock_assistant = MagicMock()
        mock_assistant.get_messages_in_thread_formatted.return_value = [
            {'role': 'user', 'content': 'Hello'},
            {'role': 'assistant', 'content': 'Hi there!'}
        ]
        mock_assistant_manager.get_assistant_by_id.return_value = mock_assistant
        
        mock_apply_regex.return_value = {'name': 'Test User', 'email': '<EMAIL>'}
        mock_validate_rules.return_value = True
        
        # Configurar para cliente MILKAUT
        self.lead_manager.client_id = MILKAUT_CLIENT_ID
        
        # Crear mock para la conversación
        mock_conversation = MagicMock()
        mock_conversation.lead = MagicMock()
        mock_conversation.additional_fields = {
            'respondio_afirmativamente_para_recibir_comunicaciones': True
        }
        mock_conversation.summary = 'Test summary'
        
        # Mockear get_final_conversation
        with patch('lead.lead_manager.LeadManager.get_final_conversation', 
                  return_value=mock_conversation) as mock_get_final_conv:
            
            # Llamar al método
            self.lead_manager.generate_lead()
            
            # Verificar que se llamó a run_integrations
            mock_run_integrations.assert_called_once()
            
            # Verificar que se estableció phone como None
            self.assertIsNone(mock_conversation.lead.phone)

    @patch('lead.lead_manager.GENERATE_LEAD_ERROR')
    @patch('lead.lead_manager.get_chatgpt_response')
    def test_get_final_conversation_error_handling(self, mock_get_chatgpt_response, mock_error_metric):
        # Configurar el mock para que falle
        mock_get_chatgpt_response.return_value = {
            'message': 'invalid json'
        }
        
        # Llamar al método que debería fallar
        result = self.lead_manager.get_final_conversation(
            all_messages=[{'role': 'user', 'content': 'test'}],
            finish_conversation_json='{}'
        )
        
        # Verificar que se llamó al contador de errores
        mock_error_metric.inc.assert_called_once()
        
        # Verificar que devuelve None en caso de error
        self.assertIsNone(result)

    @patch('lead.lead_manager.get_user_to_assign')
    def test_build_json_conversation(self, mock_get_user_to_assign):
        # Importar el modelo Message aquí para evitar problemas de importación circular
        from db.models import Message
        
        # Configurar mocks
        mock_get_user_to_assign.return_value = "test_user"
        
        # Configurar el connector en lead_manager
        self.lead_manager.connector = MagicMock()
        self.lead_manager.connector.origin = "test_origin"
        self.lead_manager.connector.origin_id = "test_origin_id"
        
        # Datos de prueba
        conversation_vars = {
            'nombre': 'Test User',
            'email': '<EMAIL>',
            'telefono': '123456789',
            'resumen': 'Test summary',
            'status': 'nuevo',
            'otro_campo': 'valor'
        }
        
        # Crear un objeto Message en lugar de un diccionario
        test_message = Message(role='user', content='test')
        chat_history = [test_message]
        token_usage = 100
        
        # Llamar al método
        conversation = self.lead_manager.build_json_conversation(
            conversation_vars=conversation_vars,
            chat_history=chat_history,
            token_usage=token_usage
        )
        
        # Verificaciones
        self.assertEqual(conversation.lead.fullname, self.lead_manager.name)
        self.assertEqual(conversation.lead.phone, self.lead_manager.mobile)
        self.assertEqual(conversation.lead.email, None)  # Se espera None según la implementación
        
        # Verificar que el chat history contiene el mensaje correcto
        self.assertEqual(len(conversation.chathistory), 1)
        self.assertEqual(conversation.chathistory[0].role, 'user')
        self.assertEqual(conversation.chathistory[0].content, 'test')
        
        self.assertEqual(conversation.status, 'nuevo')
        self.assertEqual(conversation.summary, 'Test summary')
        self.assertEqual(conversation.clientid, self.lead_manager.client_id)
        self.assertEqual(conversation.origin, 'test_origin')
        self.assertEqual(conversation.origin_id, 'test_origin_id')
        self.assertEqual(conversation.tokenusage, token_usage)
        self.assertEqual(conversation.beforeinstructionprompt, self.lead_manager.before_instruction_prompt)
        self.assertEqual(conversation.afterinstructionprompt, self.lead_manager.after_instruction_prompt)
        self.assertEqual(conversation.additional_fields['otro_campo'], 'valor')
        self.assertTrue(conversation.active)
        self.assertTrue(conversation.botstop)
        self.assertEqual(conversation.assign, 'test_user')
        
        # Verificar que se llamó a get_user_to_assign con el client_id correcto
        mock_get_user_to_assign.assert_called_once_with(self.lead_manager.client_id)

    @patch('lead.lead_manager.IntegrationManager')
    def test_run_integrations_with_integrations(self, mock_integration_manager_class):
        # Configurar
        mock_integration_manager = MagicMock()
        mock_integration_manager_class.return_value = mock_integration_manager
        
        # Configurar integraciones
        self.lead_manager.integrations = [
            {'type': 'test', 'config': {}}
        ]
        
        # Llamar al método
        conversation = MagicMock()
        self.lead_manager.run_integrations(conversation)
        
        # Verificar
        mock_integration_manager.process_integrations.assert_called_once_with(
            integrations=self.lead_manager.integrations,
            client_data=conversation
        )

    @patch('lead.lead_manager.IntegrationManager')
    def test_run_integrations_without_integrations(self, mock_integration_manager_class):
        # Configurar sin integraciones
        self.lead_manager.integrations = []
        
        # Llamar al método
        conversation = MagicMock()
        self.lead_manager.run_integrations(conversation)
        
        # Verificar que se llamó a process_integrations con lista vacía
        mock_integration_manager_class.return_value.process_integrations.assert_called_once_with(
            integrations=[],
            client_data=conversation
        )


if __name__ == '__main__':
    unittest.main()
