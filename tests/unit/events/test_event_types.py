import unittest
from events.event_types import (
    SEND_WHATSAPP_MESSAGE,
    LEAD_CREATED,
    RECEIVED_MESSAGE,
    CRM_SEND_MESSAGE,
    NOTIFY_RECEIVED_MESSAGE,
    EXCHANGE_NAME
)

class TestEventTypes(unittest.TestCase):
    """Test cases for event type constants"""
    
    def test_constants_have_correct_values(self):
        """Test that all event type constants have the expected values"""
        self.assertEqual(SEND_WHATSAPP_MESSAGE, "python.message.send")
        self.assertEqual(LEAD_CREATED, "python.lead.created")
        self.assertEqual(RECEIVED_MESSAGE, "python.message.received")
        self.assertEqual(CRM_SEND_MESSAGE, "crm.message.send")
        self.assertEqual(NOTIFY_RECEIVED_MESSAGE, "superchats.message.received")
        self.assertEqual(EXCHANGE_NAME, 'sistema.eventos')

if __name__ == '__main__':
    unittest.main()
