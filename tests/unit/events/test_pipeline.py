import unittest
from unittest.mock import MagicMock, patch

from events.pipeline import <PERSON><PERSON><PERSON>, MessagePipeline


class TestMessagePipeline(unittest.TestCase):
    def test_message_pipeline_initialization(self):
        """Test that MessagePipeline is initialized correctly"""
        mock_factory = MagicMock()
        message = MessagePipeline("Test response", mock_factory)
        self.assertEqual(message.response, "Test response")
        self.assertEqual(message.message_factory, mock_factory)
    
    def test_message_pipeline_str_representation(self):
        """Test the string representation of MessagePipeline"""
        mock_factory = MagicMock()
        mock_factory.mobile = "+1234567890"
        message = MessagePipeline("Test response", mock_factory)
        self.assertIn("Message(response=Test response, mobile=+1234567890)", str(message))

class TestPipeline(unittest.TestCase):
    def setUp(self):
        self.pipeline = Pipeline()
        self.mock_message = MagicMock(spec=MessagePipeline)
        self.mock_message.response = "Test response"
    
    @patch('logging.debug')
    def test_produce_message(self, mock_debug):
        """Test producing a message to the pipeline"""
        # The producer lock should be initially acquired in __init__
        self.pipeline.produce_message(self.mock_message, "test_producer")
        
        # Verify the message was set and consumer lock was released
        self.assertEqual(self.pipeline.message, self.mock_message)
        
        # Verify logging calls - note the format string and separate args
        mock_debug.assert_any_call("%s:waiting to produce", "test_producer")
        mock_debug.assert_any_call("%s:message produced", "test_producer")
    
    @patch('logging.debug')
    def test_consume_message(self, mock_debug):
        """Test consuming a message from the pipeline"""
        # First produce a message
        self.pipeline.produce_message(self.mock_message, "test_producer")
        
        # Reset mock to only track consume-related calls
        mock_debug.reset_mock()
        
        # Now consume it
        consumed_message = self.pipeline.consume_message("test_consumer")
        
        # Verify the correct message was returned
        self.assertEqual(consumed_message, self.mock_message)
        
        # Verify logging calls - note the format string and separate args
        mock_debug.assert_any_call("%s:waiting to consume", "test_consumer")
        mock_debug.assert_any_call("%s:message consumed", "test_consumer")
    
    def test_thread_safety(self):
        """Test that the pipeline is thread-safe"""
        import threading
        import time
        
        results = []
        
        def producer():
            for i in range(3):
                msg = MessagePipeline(f"Message {i}", MagicMock())
                self.pipeline.produce_message(msg, f"producer_{i}")
                time.sleep(0.1)
        
        def consumer():
            for _ in range(3):
                msg = self.pipeline.consume_message("consumer")
                results.append(msg.response)
                time.sleep(0.1)
        
        # Start producer and consumer threads
        producer_thread = threading.Thread(target=producer)
        consumer_thread = threading.Thread(target=consumer)
        
        producer_thread.start()
        consumer_thread.start()
        
        producer_thread.join()
        consumer_thread.join()
        
        # Verify all messages were processed in order
        self.assertEqual(len(results), 3)
        self.assertEqual(results, ["Message 0", "Message 1", "Message 2"])
