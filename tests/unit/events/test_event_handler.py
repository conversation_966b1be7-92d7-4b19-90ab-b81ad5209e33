import unittest
from unittest.mock import MagicMock, patch

# Importar solo los tipos que existen en openai 1.76.2
from openai.types.beta.threads import Message, MessageDelta
from openai.types.beta.threads.runs import RunStep


# Clases de marcador de posición para las pruebas
class MessageContentText:
    def __init__(self, text=None):
        self.text = text

class TextContentBlock:
    def __init__(self, text=None):
        self.text = text

class MessageContentTextDelta:
    def __init__(self, text=None):
        self.text = text

# Importar el manejador de eventos y la tubería
from events.event_handler import EventHandler
from events.pipeline import Pipeline


class TestEventHandler(unittest.TestCase):
    def setUp(self):
        # Configurar mocks básicos
        self.thread_id = "test_thread_123"
        self.assistant_id = "test_assistant_123"
        self.run_id = "test_run_123"
        self.message_factory = MagicMock()
        self.message_factory.hash = "test_hash_123"
        self.conversations = {self.message_factory.hash: {'token_usage': 0}}
        self.pipeline = MagicMock(spec=Pipeline)
        
        # Crear instancia del manejador de eventos
        self.handler = EventHandler(
            thread_id=self.thread_id,
            assistant_id=self.assistant_id,
            message_factory=self.message_factory,
            conversations=self.conversations,
            pipeline=self.pipeline
        )
        
        # Configurar el ID de ejecución
        self.handler.run_id = self.run_id
    
    def test_on_text_created(self):
        """Prueba el método on_text_created"""
        test_text = "Test text"
        with self.assertLogs(level='DEBUG') as log:
            self.handler.on_text_created(test_text)
            self.assertIn("assistant on_text_created", log.output[0])
    
    def test_on_text_delta(self):
        """Prueba el método on_text_delta"""
        # Crear un objeto delta con el formato esperado
        delta = MagicMock()
        delta.value = "delta text"
        snapshot = "snapshot text"
        
        with self.assertLogs(level='DEBUG') as log:
            self.handler.on_text_delta(delta, snapshot)
            self.assertTrue(any("assistant on_text_delta" in message for message in log.output))
    
    @patch('logging.debug')
    def test_on_end(self, mock_debug):
        """Prueba el método on_end"""
        # Configurar el mock para logging.debug
        def debug_side_effect(msg, *args, **kwargs):
            # Simular el comportamiento de logging.debug
            if args and args[0] is not None:
                return f"{msg}{args[0]}"
            return msg
            
        mock_debug.side_effect = debug_side_effect
        
        # Ejecutar el método
        self.handler.on_end()
        
        # Verificar que se llamó a logging.debug con el mensaje esperado
        # La implementación pasa None como segundo argumento
        mock_debug.assert_called_with("\n end assistant > ", None)
    
    def test_on_exception(self):
        """Prueba el método on_exception"""
        exception = Exception("Test exception")
        with self.assertLogs(level='DEBUG') as log:
            self.handler.on_exception(exception)
            self.assertIn("Test exception", log.output[0])
    
    def test_on_message_created(self):
        """Prueba el método on_message_created"""
        message = MagicMock(spec=Message)
        with self.assertLogs(level='DEBUG') as log:
            self.handler.on_message_created(message)
            self.assertIn("assistant on_message_created", log.output[0])
    
    def test_on_message_done_completed(self):
        """Prueba el método on_message_done con estado 'completed'"""
        # Configurar un mensaje completado
        message = MagicMock()
        message.status = 'completed'
        
        # Configurar el contenido del mensaje
        text_content = MagicMock()
        text_content.type = 'text'
        
        # Configurar el texto del mensaje
        text_obj = MagicMock()
        text_obj.value = "Test response"
        text_content.text = text_obj
        
        # Configurar el contenido del mensaje principal
        message.content = [text_content]
        
        # Configurar el mock para el pipeline
        self.pipeline.produce_message = MagicMock()
        
        # Ejecutar el método
        self.handler.on_message_done(message)
        
        # Verificar que se llamó a produce_message con los argumentos correctos
        self.pipeline.produce_message.assert_called_once()
        args, kwargs = self.pipeline.produce_message.call_args
        self.assertEqual(kwargs.get('name'), "MessageDone")
    
    @patch('events.event_handler.client')
    def test_on_message_done_failed(self, mock_client):
        """Prueba el método on_message_done con estado fallido"""
        # Configurar un mensaje fallido
        message = MagicMock(spec=Message)
        message.status = 'failed'
        
        # Ejecutar el método y verificar que se registra el error
        with self.assertLogs(level='ERROR') as log:
            self.handler.on_message_done(message)
            self.assertIn("Failed Message", log.output[0])
    
    def test_on_message_delta_method(self):
        """Prueba el método on_message_delta"""
        delta = MagicMock(spec=MessageDelta)
        snapshot = MagicMock(spec=Message)
        
        with self.assertLogs(level='DEBUG') as log:
            self.handler.on_message_delta(delta, snapshot)
            self.assertIn("assistant on_message_delta", log.output[0])
    
    @patch('events.event_handler.client')
    def test_on_tool_call_created(self, mock_client):
        """Prueba el método on_tool_call_created"""
        # Configurar un tool call
        tool_call = MagicMock()
        tool_call.function.name = "test_function"
        tool_call.id = "test_tool_id"
        
        # Configurar el run_step
        self.handler.run_step = MagicMock()
        self.handler.run_step.status = "in_progress"
        
        # Configurar el mock del cliente para simular la respuesta de la API
        mock_run = MagicMock()
        mock_run.status = "completed"
        mock_client.beta.threads.runs.retrieve.return_value = mock_run
        
        # Ejecutar el método
        with self.assertLogs(level='DEBUG') as log:
            self.handler.on_tool_call_created(tool_call)
            
            # Verificar que se actualizaron los atributos
            self.assertEqual(self.handler.function_name, "test_function")
            self.assertEqual(self.handler.tool_id, "test_tool_id")
            
            # Verificar que se llamó a la API de OpenAI
            mock_client.beta.threads.runs.retrieve.assert_called_with(
                thread_id=self.thread_id,
                run_id=self.run_id
            )
    
    @patch('events.event_handler.client')
    @patch('logging.debug')
    def test_on_tool_call_done(self, mock_debug, mock_client):
        """Prueba el método on_tool_call_done"""
        # Configurar un tool call
        tool_call = MagicMock()
        tool_call.id = "test_tool_id"
        
        # Configurar el mock del cliente para simular la respuesta de la API
        mock_run = MagicMock()
        mock_run.status = "completed"
        mock_client.beta.threads.runs.retrieve.return_value = mock_run
        
        # Configurar la respuesta de la API para listar mensajes
        mock_message = MagicMock()
        
        # Configurar el contenido del mensaje
        text_content = MagicMock()
        text_content.type = 'text'
        text_content.text = MagicMock()
        text_content.text.value = "Test message content"
        
        # Configurar el contenido del mensaje principal
        mock_message.content = [text_content]
        
        # Configurar la respuesta de la API
        mock_messages = MagicMock()
        mock_messages.data = [mock_message]
        mock_client.beta.threads.messages.list.return_value = mock_messages
        
        # Configurar el mock para logging.debug
        def debug_side_effect(msg, *args, **kwargs):
            # Simular el comportamiento de logging.debug
            if args and args[0] is not None:
                return f"{msg}{args[0]}"
            return msg
            
        mock_debug.side_effect = debug_side_effect
        
        # Ejecutar el método
        self.handler.on_tool_call_done(tool_call)
        
        # Verificar que se llamó a la API de OpenAI
        mock_client.beta.threads.runs.retrieve.assert_called_with(
            thread_id=self.thread_id,
            run_id=self.run_id
        )
    
    @patch('logging.debug')
    def test_on_run_step_created(self, mock_debug):
        """Prueba el método on_run_step_created"""
        # Configurar un run step
        run_step = MagicMock()
        run_step.run_id = "new_run_id_123"
        run_step.status = "in_progress"  # Asegurarse de que el estado es válido
        
        # Configurar el mock para logging.debug
        def debug_side_effect(msg, *args, **kwargs):
            # Simular el comportamiento de logging.debug
            if args and isinstance(args[0], str):
                return msg % args[0]
            return msg
            
        mock_debug.side_effect = debug_side_effect
        
        # Ejecutar el método
        self.handler.on_run_step_created(run_step)
        
        # Verificar que se actualizaron los atributos
        self.assertEqual(self.handler.run_id, "new_run_id_123")
        self.assertEqual(self.handler.run_step, run_step)
    
    def test_on_run_step_done(self):
        """Prueba el método on_run_step_done"""
        # Configurar un run step con uso de tokens
        run_step = MagicMock(spec=RunStep)
        run_step.usage = MagicMock()
        run_step.usage.total_tokens = 100
        
        # Configurar el hash de conversación
        test_hash = "test_hash_123"
        self.message_factory.hash = test_hash
        self.conversations[test_hash] = {'token_usage': 50}
        
        # Ejecutar el método
        self.handler.on_run_step_done(run_step)
        
        # Verificar que se actualizó el contador de tokens
        self.assertEqual(self.conversations[test_hash]['token_usage'], 150)
    
    def test_on_tool_call_delta_function(self):
        """Prueba el método on_tool_call_delta para una función"""
        # Configurar un delta de función
        delta = MagicMock()
        delta.type = 'function'
        delta.function = MagicMock()
        delta.function.arguments = '{"key": "value"}'
        
        # Ejecutar el método
        with self.assertLogs(level='DEBUG') as log:
            self.handler.on_tool_call_delta(delta, None)
            
            # Verificar que se actualizaron los argumentos
            self.assertEqual(self.handler.arguments, '{"key": "value"}')
    
    @patch('logging.debug')
    def test_on_tool_call_delta_code_interpreter(self, mock_debug):
        """Prueba el método on_tool_call_delta para un intérprete de código"""
        # Configurar un delta de intérprete de código
        delta = MagicMock()
        delta.type = 'code_interpreter'
        delta.code_interpreter = MagicMock()
        delta.code_interpreter.input = "print('hello')"
        
        # Crear un mock para la salida del intérprete
        output = MagicMock()
        output.type = "logs"
        output.logs = "hello\n"
        delta.code_interpreter.outputs = [output]
        
        # Configurar el mock para logging.debug
        def debug_side_effect(msg, *args, **kwargs):
            # Simular el comportamiento de logging.debug
            if args and args[0] is not None:
                return f"{msg}{args[0]}"
            return msg
            
        mock_debug.side_effect = debug_side_effect
        
        # Ejecutar el método
        self.handler.on_tool_call_delta(delta, None)
        
        # Obtener todos los mensajes de depuración registrados
        debug_messages = []
        for call_args in mock_debug.call_args_list:
            args, kwargs = call_args
            debug_messages.append(args[0] if args else '')
        
        # Verificar que los mensajes esperados están en los registros
        expected_messages = [
            'on_tool_call_delta > code_interpreter',
            "print('hello')",
            '\n\noutput >',
            '\nhello\n'
        ]
        
        for expected_msg in expected_messages:
            self.assertIn(expected_msg, debug_messages, 
                        f"Expected message not found in debug logs: {expected_msg}")
    
    def test_on_event_requires_action(self):
        """Prueba el método on_event para el evento 'thread.run.requires_action'"""
        # Configurar un evento
        event = MagicMock()
        event.event = "thread.run.requires_action"
        
        # Configurar argumentos
        self.handler.arguments = '{"key": "value"}'
        
        # Ejecutar el método
        with self.assertLogs(level='DEBUG') as log:
            self.handler.on_event(event)
            
            # Verificar que se registró el evento
            self.assertIn("thread.run.requires_action", "".join(log.output))
            self.assertIn("ARGS:", "".join(log.output))

if __name__ == '__main__':
    unittest.main()
