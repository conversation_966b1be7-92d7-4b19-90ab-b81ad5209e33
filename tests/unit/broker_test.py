import unittest
from unittest.mock import MagicMock, patch

from broker.broker import Que<PERSON><PERSON>anager
from enums.connector_types import ConnectorType


class TestQueueManager(unittest.TestCase):
    def setUp(self):
        self.client_service_mock = MagicMock()
        self.connectors_mock = MagicMock()
        self.queue_manager = QueueManager(self.client_service_mock, self.connectors_mock)
        
        # Mock the brokers dictionary
        self.mock_broker = MagicMock()
        self.queue_manager.brokers = {
            ConnectorType.WHATSAPP_VENOM: self.mock_broker,
            ConnectorType.WHATSAPP_API: MagicMock()
        }

    def test_hook_with_valid_client_id(self):
        """Test the hook method with a valid client ID in the request data."""
        # Test data
        request_data = {
            'clientId': 'valid_client_id',
            'mobile': '123456789',
            'message': 'Test message'
        }
        
        # Mock the broker's hook method
        self.mock_broker.hook.return_value = ("Success", 200)
        
        # Call the hook method
        result = self.queue_manager.hook(request_data, ConnectorType.WHATSAPP_VENOM)
        
        # Verify the result
        self.assertEqual(result, ("Success", 200))
        self.mock_broker.hook.assert_called_once_with(
            request_data=request_data,
            client_id='valid_client_id'
        )
    
    def test_hook_without_client_id(self):
        """Test the hook method without a client ID in the request data and no request context."""
        # Test data without clientId
        request_data = {
            'mobile': '123456789',
            'message': 'Test message'
        }
        
        # Call the hook method without clientId in request_data and no request context
        with patch('broker.broker.has_request_context', return_value=False):
            result = self.queue_manager.hook(request_data, ConnectorType.WHATSAPP_VENOM)
        
        # Verify the error response
        self.assertEqual(result, ("client.id parameter is missing", 400))
        self.mock_broker.hook.assert_not_called()
    
    def test_hook_with_unsupported_connector(self):
        """Test the hook method with an unsupported connector type."""
        # Test data
        request_data = {
            'clientId': 'valid_client_id',
            'mobile': '123456789',
            'message': 'Test message'
        }
        
        # Call the hook method with an unsupported connector type
        result = self.queue_manager.hook(request_data, "UNSUPPORTED_CONNECTOR")
        
        # Verify the error response
        self.assertEqual(result, ("Unsupported connector type", 400))
        self.mock_broker.hook.assert_not_called()
    
    def test_hook_with_client_id_in_request_context(self):
        """Test the hook method with client ID from request context."""
        # Test data with clientId in the request data
        request_data = {
            'clientId': 'client_from_context',
            'mobile': '123456789',
            'message': 'Test message'
        }
        
        # Mock the broker's hook method
        self.mock_broker.hook.return_value = ("Success", 200)
        
        # Call the hook method
        result = self.queue_manager.hook(request_data, ConnectorType.WHATSAPP_VENOM)
        
        # Verify the result
        self.assertEqual(result, ("Success", 200))
        self.mock_broker.hook.assert_called_once_with(
            request_data=request_data,
            client_id='client_from_context'
        )


if __name__ == '__main__':
    unittest.main()
