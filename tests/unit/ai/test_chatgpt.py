import os
from unittest.mock import patch, MagicMock

from ai import chatgpt

# Test data
TEST_QUESTIONS = [{"role": "user", "content": "¿Cuál es la capital de Francia?"}]
TEST_RESPONSE = {
    "message": "La capital de Francia es París.",
    "bill": 42
}

@patch('ai.chatgpt.get_client_instance')
def test_get_chatgpt_response_json(mock_get_client):
    """Test get_chatgpt_response with json_response=True"""
    # Setup mock
    mock_client = MagicMock()
    mock_response = MagicMock()
    mock_choice = MagicMock()
    mock_message = MagicMock()
    mock_usage = MagicMock()
    
    mock_message.content = '{"answer": "La capital de Francia es París."}'
    mock_choice.message = mock_message
    mock_response.choices = [mock_choice]
    mock_response.usage = mock_usage
    mock_usage.total_tokens = 42
    
    mock_client.chat.completions.create.return_value = mock_response
    mock_get_client.return_value = mock_client
    
    # Call the function
    result = chatgpt.get_chatgpt_response(TEST_QUESTIONS, json_response=True)
    
    # Verify the result
    assert result["message"] == '{"answer": "La capital de Francia es París."}'
    assert result["bill"] == 42
    
    # Verify the API was called with the correct parameters
    mock_client.chat.completions.create.assert_called_once()
    call_args = mock_client.chat.completions.create.call_args[1]
    assert call_args["model"] == chatgpt.GPT_MODEL
    assert call_args["messages"] == TEST_QUESTIONS
    assert call_args["temperature"] == 0.3
    assert call_args["max_tokens"] == 300
    assert call_args["response_format"] == {"type": "json_object"}

@patch('ai.chatgpt.get_client_instance')
def test_get_chatgpt_response_text(mock_get_client):
    """Test get_chatgpt_response with json_response=False"""
    # Setup mock
    mock_client = MagicMock()
    mock_response = MagicMock()
    mock_choice = MagicMock()
    mock_message = MagicMock()
    mock_usage = MagicMock()
    
    mock_message.content = "La capital de Francia es París."
    mock_choice.message = mock_message
    mock_response.choices = [mock_choice]
    mock_response.usage = mock_usage
    mock_usage.total_tokens = 42
    
    mock_client.chat.completions.create.return_value = mock_response
    mock_get_client.return_value = mock_client
    
    # Call the function
    result = chatgpt.get_chatgpt_response(TEST_QUESTIONS, json_response=False)
    
    # Verify the result
    assert result["message"] == "La capital de Francia es París."
    assert result["bill"] == 42
    
    # Verify the API was called without the response_format parameter
    mock_client.chat.completions.create.assert_called_once()
    call_args = mock_client.chat.completions.create.call_args[1]
    assert "response_format" not in call_args

@patch('ai.chatgpt.get_client_instance')
def test_get_chatgpt_response_exception(mock_get_client):
    """Test get_chatgpt_response when an exception occurs"""
    # Setup mock to raise an exception
    mock_client = MagicMock()
    mock_client.chat.completions.create.side_effect = Exception("API Error")
    mock_get_client.return_value = mock_client
    
    # Call the function and verify it returns the exception
    result = chatgpt.get_chatgpt_response(TEST_QUESTIONS, json_response=False)
    assert isinstance(result, Exception)
    assert str(result) == "API Error"

def test_gpt_model_environment_variable():
    """Test that GPT_MODEL can be overridden by environment variable"""
    test_model = "gpt-4-test"
    with patch.dict(os.environ, {"GPT_MODEL": test_model}):
        # Need to reload the module to pick up the new environment variable
        import importlib
        import ai.chatgpt as chatgpt_module
        importlib.reload(chatgpt_module)
        
        assert chatgpt_module.GPT_MODEL == test_model
        
        # Clean up by reloading the original module
        importlib.reload(chatgpt)
