from unittest.mock import patch, MagicMock

from ai.assistant import Assistant, Assistant<PERSON><PERSON>ger

# Test data
TEST_ASSISTANT_ID = "asst_test123"
TEST_THREAD_ID = "thread_test123"
TEST_RUN_ID = "run_test123"
TEST_MESSAGE_ID = "msg_test123"

def test_get_zoneinfo_caching():
    """Test that get_zoneinfo caches timezone objects"""
    from ai.assistant import get_zoneinfo
    
    # First call should create a new ZoneInfo object
    tz1 = get_zoneinfo("America/New_York")
    
    # Second call with same timezone should return the same object
    tz2 = get_zoneinfo("America/New_York")
    
    # Different timezone should return a different object
    tz3 = get_zoneinfo("Europe/Paris")
    
    assert tz1 is tz2
    assert tz1 is not tz3
    assert str(tz1) == "America/New_York"

@patch('ai.assistant.get_client_instance')
def test_assistant_initialization(mock_get_client):
    """Test that Assistant is initialized correctly"""
    # Setup mock
    mock_client = MagicMock()
    mock_get_client.return_value = mock_client
    
    # Create assistant
    assistant = Assistant(TEST_ASSISTANT_ID)
    
    # Verify initialization
    assert assistant.assistant_id == TEST_ASSISTANT_ID
    assert assistant.client is mock_client

@patch('ai.assistant.get_client_instance')
def test_create_thread(mock_get_client):
    """Test that create_thread creates a new thread"""
    # Setup mock
    mock_client = MagicMock()
    mock_thread = MagicMock()
    mock_thread.id = TEST_THREAD_ID
    mock_client.beta.threads.create.return_value = mock_thread
    mock_get_client.return_value = mock_client
    
    # Create assistant and call method
    assistant = Assistant(TEST_ASSISTANT_ID)
    result = assistant.create_thread()
    
    # Verify the result
    assert result is mock_thread
    mock_client.beta.threads.create.assert_called_once()

class MockRole:
    def __init__(self, role_str):
        self.role_str = role_str
    
    def to_string(self):
        return self.role_str

@patch('ai.assistant.get_client_instance')
def test_create_user_message(mock_get_client):
    """Test that create_user_message adds a message to a thread"""
    # Setup mock
    mock_client = MagicMock()
    mock_message = MagicMock()
    mock_client.beta.threads.messages.create.return_value = mock_message
    mock_get_client.return_value = mock_client
    
    # Test data
    test_content = "Hello, world!"
    test_role = MockRole("user")
    
    # Create assistant and call method
    assistant = Assistant(TEST_ASSISTANT_ID)
    result = assistant.create_user_message(TEST_THREAD_ID, test_content, test_role)
    
    # Verify the result
    assert result is mock_message
    
    # Verify the message was created with the correct parameters
    mock_client.beta.threads.messages.create.assert_called_once()
    call_args = mock_client.beta.threads.messages.create.call_args[1]
    assert call_args["thread_id"] == TEST_THREAD_ID
    assert call_args["role"] == "user"
    assert test_content in call_args["content"]
    assert "|" in call_args["content"]  # Should contain timestamp

@patch('ai.assistant.get_client_instance')
def test_run_assistant(mock_get_client):
    """Test that run_assistant starts a run on a thread"""
    # Setup mock
    mock_client = MagicMock()
    mock_run = MagicMock()
    mock_run.id = TEST_RUN_ID
    mock_client.beta.threads.runs.create.return_value = mock_run
    mock_get_client.return_value = mock_client
    
    # Create assistant and call method
    assistant = Assistant(TEST_ASSISTANT_ID)
    result = assistant.run_assistant(TEST_THREAD_ID)
    
    # Verify the result
    assert result is mock_run
    
    # Verify the run was created with the correct parameters
    mock_client.beta.threads.runs.create.assert_called_once_with(
        thread_id=TEST_THREAD_ID,
        assistant_id=TEST_ASSISTANT_ID
    )

@patch('ai.assistant.get_client_instance')
def test_get_run(mock_get_client):
    """Test that get_run retrieves a run"""
    # Setup mock
    mock_client = MagicMock()
    mock_run = MagicMock()
    mock_client.beta.threads.runs.retrieve.return_value = mock_run
    mock_get_client.return_value = mock_client
    
    # Create assistant and call method
    assistant = Assistant(TEST_ASSISTANT_ID)
    result = assistant.get_run(TEST_THREAD_ID, TEST_RUN_ID)
    
    # Verify the result
    assert result is mock_run
    
    # Verify the run was retrieved with the correct parameters
    mock_client.beta.threads.runs.retrieve.assert_called_once_with(
        thread_id=TEST_THREAD_ID,
        run_id=TEST_RUN_ID
    )

def test_assistant_manager():
    """Test that AssistantManager returns the same instance for the same ID"""
    # Get instance for the first time
    assistant1 = AssistantManager.get_assistant_by_id(TEST_ASSISTANT_ID)
    
    # Get instance for the same ID again
    assistant2 = AssistantManager.get_assistant_by_id(TEST_ASSISTANT_ID)
    
    # Get instance for a different ID
    assistant3 = AssistantManager.get_assistant_by_id("different_id")
    
    # Verify the results
    assert assistant1 is assistant2
    assert assistant1 is not assistant3
    assert assistant1.assistant_id == TEST_ASSISTANT_ID
    assert assistant3.assistant_id == "different_id"
