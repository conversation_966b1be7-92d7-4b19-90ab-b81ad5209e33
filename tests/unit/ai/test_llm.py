import os
import tempfile
import pytest
from unittest.mock import patch, MagicMock
from ai import llm

def test_text_to_speech():
    """Test that text_to_speech creates an audio file with the given text."""
    test_text = "<PERSON><PERSON>, esto es una prueba"
    
    # Call the function
    audio_file = llm.text_to_speech(test_text)
    
    # Verify the file was created and is not empty
    assert os.path.exists(audio_file.name)
    assert os.path.getsize(audio_file.name) > 0
    
    # Clean up
    audio_file.close()
    os.unlink(audio_file.name)

def test_delete_audio_file():
    """Test that delete_audio_file removes the specified file."""
    # Create a temporary file
    temp = tempfile.NamedTemporaryFile(delete=False)
    temp_path = temp.name
    temp.close()
    
    # Verify file exists
    assert os.path.exists(temp_path)
    
    # Call the function
    llm.delete_audio_file(temp_path)
    
    # Verify file was deleted
    assert not os.path.exists(temp_path)

def test_change_rate():
    """Test that change_rate sets the engine rate correctly."""
    mock_engine = MagicMock()
    test_rate = 200
    
    # Call the function
    llm.change_rate(mock_engine, test_rate)
    
    # Verify the rate was set correctly
    mock_engine.setProperty.assert_called_once_with('rate', test_rate)

@patch('ai.llm.pyttsx4.init')
def test_call_speaker(mock_init):
    """Test that call_speaker generates speech and returns an audio file."""
    # Setup mock engine and voices
    mock_engine = MagicMock()
    mock_voice = MagicMock()
    mock_voice.languages = ['es_ES']
    mock_voice.gender = 'VoiceGenderFemale'
    mock_voice.id = 'test_voice_1'
    mock_engine.getProperty.return_value = [mock_voice]
    mock_init.return_value = mock_engine
    
    test_text = "Test speech"
    test_config = {
        "language": "es_ES",
        "gender": "F",
        "rate": 150
    }
    
    # Call the function
    audio_file = llm.call_speaker(test_text, test_config)
    
    # Verify the engine was initialized and configured
    mock_init.assert_called_once()
    mock_engine.save_to_file.assert_called_once_with(test_text, audio_file.name)
    mock_engine.runAndWait.assert_called_once()
    mock_engine.setProperty.assert_any_call('voice', 'test_voice_1')
    mock_engine.setProperty.assert_any_call('rate', 150)
    
    # Clean up
    audio_file.close()
    os.unlink(audio_file.name)

@patch('ai.llm.pyttsx4.init')
def test_change_voice_success(mock_init):
    """Test that change_voice sets the voice when a matching voice is found."""
    # Setup mock engine with test voices
    mock_engine = MagicMock()
    mock_voice1 = MagicMock()
    mock_voice1.languages = ['es_ES']
    mock_voice1.gender = 'VoiceGenderFemale'
    mock_voice1.id = 'test_voice_1'
    
    # Configure getProperty to return the test voice
    def get_property_side_effect(prop):
        if prop == 'voices':
            return [mock_voice1]
        return None
    
    mock_engine.getProperty.side_effect = get_property_side_effect
    
    # Call the function
    result = llm.change_voice(mock_engine, 'es_ES', 'F')
    
    # Verify the voice was set
    assert result is True
    mock_engine.setProperty.assert_called_once_with('voice', 'test_voice_1')

@patch('ai.llm.pyttsx4.init')
def test_change_voice_not_found(mock_init):
    """Test that change_voice raises an exception when no matching voice is found."""
    # Setup mock engine with no matching voices
    mock_engine = MagicMock()
    mock_voice1 = MagicMock()
    mock_voice1.languages = ['en_US']
    mock_voice1.gender = 'VoiceGenderMale'
    
    mock_engine.get_property.return_value = [mock_voice1]
    
    # Verify the function raises an exception
    with pytest.raises(RuntimeError):
        llm.change_voice(mock_engine, 'es_ES', 'F')
