import unittest
from utils.utils import extract_regex


class MyTestUtilsCase(unittest.TestCase):
    def test_patente_nueva_regex(self):
        regex_s = r"^(?:[A-Z]{2}\s?\d{2}\s?[A-Z]{2}|[A-Z]{3}\s?\d{3})$"
        test_str = "AA11SA"
        result = extract_regex(test_str, regex_s)
        self.assertEqual(result, "AA11SA")

    def test_patente_nueva_con_espacios_regex(self):
        regex_s = r"^(?:[A-Z]{2}\s?\d{2}\s?[A-Z]{2}|[A-Z]{3}\s?\d{3})$"
        test_str = "AA 11 SA"
        result = extract_regex(test_str, regex_s)
        self.assertEqual(result, "AA 11 SA")

    def test_patente_vieja_regex(self):
        regex_s = r"^(?:[A-Z]{2}\s?\d{2}\s?[A-Z]{2}|[A-Z]{3}\s?\d{3})$"
        test_str = "GTR945"
        result = extract_regex(test_str, regex_s)
        self.assertEqual(result, "GTR945")

    def test_patente_vieja_con_espacios_regex(self):
        regex_s = r"^(?:[A-Z]{2}\s?\d{2}\s?[A-Z]{2}|[A-Z]{3}\s?\d{3})$"
        test_str = "GOR 123"
        result = extract_regex(test_str, regex_s)
        self.assertEqual(result, "GOR 123")


if __name__ == '__main__':
    unittest.main()
