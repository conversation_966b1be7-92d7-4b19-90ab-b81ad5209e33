"""Tests for text_message module."""
import unittest
from unittest.mock import MagicMock, patch

from message.text_message import TextMessageInput, TextMessageOutput


class TestTextMessageInput(unittest.TestCase):
    """Test cases for TextMessageInput class."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.mock_conversations = {}
        self.mock_conversations_service = MagicMock()
        self.mock_pipeline = MagicMock()
        self.mock_message_factory = MagicMock()
        self.mock_cache_service = MagicMock()
        
        # Configurar el mock del conector
        self.mock_connector = MagicMock()
        self.mock_message_factory.connector = self.mock_connector
        self.mock_message_factory.data = {}
        
        # Crear instancia bajo prueba
        self.text_message = TextMessageInput(
            conversations=self.mock_conversations,
            conversations_service=self.mock_conversations_service,
            pipeline=self.mock_pipeline,
            message_factory=self.mock_message_factory,
            cache_service=self.mock_cache_service
        )
        
        # Configurar conversación
        self.text_message.conversation_db = MagicMock()
        self.text_message.conversation_db.id = "test_conversation_id"
        
        # Mock para los métodos de la clase base
        self.text_message.delete_conversation_and_generate_lead = MagicMock()
        self.text_message.delete_conversation = MagicMock()
        self.text_message.add_message_to_chathistory = MagicMock()
        self.text_message.send_notification_new_message_to_crm = MagicMock()
        self.text_message.process_and_run_assistant = MagicMock()
    
    def test_handle_end_command(self):
        """Test handling of #end command."""
        self.mock_connector.get_message.return_value = "#end"
        
        self.text_message.handle(stop_bot=False)
        
        self.text_message.delete_conversation_and_generate_lead.assert_called_once()
        self.text_message.process_and_run_assistant.assert_not_called()
    
    def test_handle_restart_command(self):
        """Test handling of #restart command."""
        self.mock_connector.get_message.return_value = "#restart"
        
        self.text_message.handle(stop_bot=False)
        
        self.text_message.delete_conversation.assert_called_once()
        self.text_message.process_and_run_assistant.assert_not_called()
    
    def test_handle_stop_bot(self):
        """Test handling when stop_bot is True."""
        test_message = "Test message"
        self.mock_connector.get_message.return_value = test_message
        
        self.text_message.handle(stop_bot=True)
        
        self.text_message.add_message_to_chathistory.assert_called_once_with(
            message=test_message,
            conversation_id="test_conversation_id"
        )
        self.text_message.send_notification_new_message_to_crm.assert_called_once_with(
            message=test_message
        )
        self.text_message.process_and_run_assistant.assert_not_called()
    
    def test_handle_normal_message(self):
        """Test handling of normal message."""
        test_message = "Hello, this is a test"
        self.mock_connector.get_message.return_value = test_message
        
        self.text_message.handle(stop_bot=False)
        
        self.text_message.process_and_run_assistant.assert_called_once_with(
            message=test_message
        )
        self.text_message.add_message_to_chathistory.assert_not_called()
        self.text_message.send_notification_new_message_to_crm.assert_not_called()


class TestTextMessageOutput(unittest.TestCase):
    """Test cases for TextMessageOutput class."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.mock_conversations = {}
        self.mock_conversations_service = MagicMock()
        self.mock_message_factory = MagicMock()
        self.mock_connector = MagicMock()
        self.mock_message_factory.connector = self.mock_connector
        self.mock_cache_service = MagicMock()
        
        # Configurar el tiempo de finalización de conversación
        self.mock_message_factory.finish_conversation_time_in_seconds = 1800  # 30 minutos
        
        # Crear instancia bajo prueba
        self.text_message = TextMessageOutput(
            conversations=self.mock_conversations,
            conversations_service=self.mock_conversations_service,
            message_factory=self.mock_message_factory,
            cache_service=self.mock_cache_service
        )
        
        # Mock para los métodos de la clase base
        self.text_message.schedule_delete = MagicMock()
    
    @patch('logging.info')
    @patch('logging.error')
    def test_send_message_to_connector_success(self, mock_error, mock_info):
        """Test successful message sending to connector."""
        test_message = "Test message"
        test_mobile = "+1234567890"
        
        self.text_message.send_message_to_connector(
            message=test_message,
            mobile=test_mobile
        )
        
        # Verificar que se llamó al conector correctamente
        self.mock_connector.send_message.assert_called_once_with(
            test_message, test_mobile
        )
        
        # Verificar el logging
        mock_info.assert_called_once_with(f"Message Response; {test_message}")
        mock_error.assert_not_called()
        
        # Verificar que se programó la eliminación
        self.text_message.schedule_delete.assert_called_once_with(
            test_mobile,
            self.mock_message_factory.finish_conversation_time_in_seconds
        )
    
    @patch('logging.error')
    def test_send_message_to_connector_error(self, mock_error):
        """Test error handling when sending message fails."""
        test_message = "Test message"
        test_mobile = "+1234567890"
        test_error = Exception("Connection error")
        
        # Configurar el mock para que falle
        self.mock_connector.send_message.side_effect = test_error
        
        self.text_message.send_message_to_connector(
            message=test_message,
            mobile=test_mobile
        )
        
        # Verificar que se registró el error
        mock_error.assert_called_once_with(
            f"Error send message in connector: {str(test_error)}"
        )
        
        # Verificar que aún así se intentó programar la eliminación
        self.text_message.schedule_delete.assert_called_once()


if __name__ == '__main__':
    unittest.main()
