"""Tests for video_message module."""
import unittest
from unittest.mock import MagicMock, patch

from message.video_message import VideoMessageInput, VideoMessageOutput


class TestVideoMessageInput(unittest.TestCase):
    """Test cases for VideoMessageInput class."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.mock_conversations = {}
        self.mock_conversations_service = MagicMock()
        self.mock_pipeline = MagicMock()
        self.mock_message_factory = MagicMock()
        self.mock_connector = MagicMock()
        self.mock_message_factory.connector = self.mock_connector
        self.mock_message_factory.data = {}
        self.mock_message_factory.mobile = "+1234567890"
        self.mock_cache_service = MagicMock()
        
        # Crear instancia bajo prueba
        self.video_message = VideoMessageInput(
            conversations=self.mock_conversations,
            conversations_service=self.mock_conversations_service,
            pipeline=self.mock_pipeline,
            message_factory=self.mock_message_factory,
            cache_service=self.mock_cache_service
        )
        
        # Configurar el mock para get_video
        self.test_video_data = {
            "id": "test_video_id",
            "mime_type": "video/mp4"
        }
        self.mock_connector.get_video.return_value = self.test_video_data
        
        # Configurar el mock para query_media_url
        self.test_media_url = "https://example.com/video.mp4"
        self.mock_connector.query_media_url.return_value = self.test_media_url
        
        # Configurar el mock para download_media
        self.test_filename = "downloaded_video.mp4"
        self.mock_connector.download_media.return_value = self.test_filename
    
    @patch('message.video_message.logging')
    def test_handle_video_processing(self, mock_logging):
        """Test successful video processing."""
        # Llamar al método bajo prueba
        self.video_message.handle(stop_bot=False)
        
        # Verificar que se obtuvo el video
        self.mock_connector.get_video.assert_called_once_with({})
        
        # Verificar que se consultó la URL del medio
        self.mock_connector.query_media_url.assert_called_once_with(
            self.test_video_data["id"]
        )
        
        # Verificar que se descargó el video
        self.mock_connector.download_media.assert_called_once_with(
            self.test_media_url, self.test_video_data["mime_type"]
        )
        
        # Verificar el registro de log
        mock_logging.info.assert_called_once_with(
            f"{self.mock_message_factory.mobile} sent video {self.test_filename}"
        )


class TestVideoMessageOutput(unittest.TestCase):
    """Test cases for VideoMessageOutput class."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.mock_conversations = {}
        self.mock_conversations_service = MagicMock()
        self.mock_message_factory = MagicMock()
        self.mock_connector = MagicMock()
        self.mock_message_factory.connector = self.mock_connector
        self.mock_cache_service = MagicMock()
        
        # Crear instancia bajo prueba
        self.video_message = VideoMessageOutput(
            conversations=self.mock_conversations,
            conversations_service=self.mock_conversations_service,
            message_factory=self.mock_message_factory,
            cache_service=self.mock_cache_service
        )
    
    def test_send_message_to_connector(self):
        """Test placeholder for send_message_to_connector."""
        # Este es un método placeholder que no hace nada en la implementación actual
        test_message = "Test message"
        test_mobile = "+1234567890"
        
        # Llamar al método bajo prueba
        self.video_message.send_message_to_connector(test_message, test_mobile)
        
        # Verificar que el método no hace nada (comportamiento actual)
        # En una implementación real, aquí irían las aserciones apropiadas
        pass


if __name__ == '__main__':
    unittest.main()
