"""Tests for location_message module."""
import unittest
from unittest.mock import MagicMock, patch

from message.location_message import LocationMessageInput, LocationMessageOutput


class TestLocationMessageInput(unittest.TestCase):
    """Test cases for LocationMessageInput class."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.mock_conversations = {}
        self.mock_conversations_service = MagicMock()
        self.mock_pipeline = MagicMock()
        self.mock_message_factory = MagicMock()
        self.mock_connector = MagicMock()
        self.mock_message_factory.connector = self.mock_connector
        self.mock_message_factory.data = {}
        self.mock_cache_service = MagicMock()
        
        # Crear instancia bajo prueba
        self.location_message = LocationMessageInput(
            conversations=self.mock_conversations,
            conversations_service=self.mock_conversations_service,
            pipeline=self.mock_pipeline,
            message_factory=self.mock_message_factory,
            cache_service=self.mock_cache_service
        )
        
        # Configurar el mock para get_location
        self.test_location_data = {
            "latitude": "-34.603722",
            "longitude": "-58.381592"
        }
        self.mock_connector.get_location.return_value = self.test_location_data
    
    @patch('message.location_message.logging')
    def test_handle_location_processing(self, mock_logging):
        """Test successful location processing."""
        # Llamar al método bajo prueba
        self.location_message.handle(stop_bot=False)
        
        # Verificar que se obtuvo la ubicación
        self.mock_connector.get_location.assert_called_once_with({})
        
        # Verificar el registro de log
        mock_logging.info.assert_called_once_with(
            "Location: %s, %s", 
            self.test_location_data["latitude"], 
            self.test_location_data["longitude"]
        )


class TestLocationMessageOutput(unittest.TestCase):
    """Test cases for LocationMessageOutput class."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.mock_conversations = {}
        self.mock_conversations_service = MagicMock()
        self.mock_message_factory = MagicMock()
        self.mock_connector = MagicMock()
        self.mock_message_factory.connector = self.mock_connector
        self.mock_cache_service = MagicMock()
        
        # Crear instancia bajo prueba
        self.location_message = LocationMessageOutput(
            conversations=self.mock_conversations,
            conversations_service=self.mock_conversations_service,
            message_factory=self.mock_message_factory,
            cache_service=self.mock_cache_service
        )
    
    def test_send_message_to_connector(self):
        """Test placeholder for send_message_to_connector."""
        # Este es un método placeholder que no hace nada en la implementación actual
        test_message = "Test message"
        test_mobile = "+1234567890"
        
        # Llamar al método bajo prueba
        self.location_message.send_message_to_connector(test_message, test_mobile)
        
        # Verificar que el método no hace nada (comportamiento actual)
        # En una implementación real, aquí irían las aserciones apropiadas
        pass


if __name__ == '__main__':
    unittest.main()
