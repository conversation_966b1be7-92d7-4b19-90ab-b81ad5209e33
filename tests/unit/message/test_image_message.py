"""Tests for image_message module."""
import unittest
from unittest.mock import MagicMock, patch

from message.image_message import ImageMessageInput, ImageMessageOutput


class TestImageMessageInput(unittest.TestCase):
    """Test cases for ImageMessageInput class."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.mock_conversations = {}
        self.mock_conversations_service = MagicMock()
        self.mock_pipeline = MagicMock()
        self.mock_message_factory = MagicMock()
        self.mock_connector = MagicMock()
        self.mock_message_factory.connector = self.mock_connector
        self.mock_message_factory.data = {}
        self.mock_message_factory.mobile = "+1234567890"
        self.mock_cache_service = MagicMock()
        
        # Crear instancia bajo prueba
        self.image_message = ImageMessageInput(
            conversations=self.mock_conversations,
            conversations_service=self.mock_conversations_service,
            pipeline=self.mock_pipeline,
            message_factory=self.mock_message_factory,
            cache_service=self.mock_cache_service
        )
        
        # Configurar el mock para get_image
        self.test_image_data = {
            "id": "test_image_id",
            "mime_type": "image/jpeg"
        }
        self.mock_connector.get_image.return_value = self.test_image_data
        
        # Configurar el mock para query_media_url
        self.test_media_url = "https://example.com/image.jpg"
        self.mock_connector.query_media_url.return_value = self.test_media_url
        
        # Configurar el mock para download_media
        self.test_filename = "downloaded_image.jpg"
        self.mock_connector.download_media.return_value = self.test_filename
    
    @patch('message.image_message.logging')
    def test_handle_image_processing(self, mock_logging):
        """Test successful image processing."""
        # Llamar al método bajo prueba
        self.image_message.handle()
        
        # Verificar que se obtuvo la imagen
        self.mock_connector.get_image.assert_called_once_with({})
        
        # Verificar que se consultó la URL del medio
        self.mock_connector.query_media_url.assert_called_once_with(
            self.test_image_data["id"]
        )
        
        # Verificar que se descargó la imagen
        self.mock_connector.download_media.assert_called_once_with(
            self.test_media_url, self.test_image_data["mime_type"]
        )
        
        # Verificar el registro de log
        mock_logging.info.assert_called_once_with(
            f"{self.mock_message_factory.mobile} sent image {self.test_filename}"
        )


class TestImageMessageOutput(unittest.TestCase):
    """Test cases for ImageMessageOutput class."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.mock_conversations = {}
        self.mock_conversations_service = MagicMock()
        self.mock_message_factory = MagicMock()
        self.mock_connector = MagicMock()
        self.mock_message_factory.connector = self.mock_connector
        self.mock_cache_service = MagicMock()
        
        # Crear instancia bajo prueba
        self.image_message = ImageMessageOutput(
            conversations=self.mock_conversations,
            conversations_service=self.mock_conversations_service,
            message_factory=self.mock_message_factory,
            cache_service=self.mock_cache_service
        )
    
    def test_send_message_to_connector(self):
        """Test placeholder for send_message_to_connector."""
        # Este es un método placeholder que no hace nada en la implementación actual
        test_message = "Test message"
        test_mobile = "+1234567890"
        
        # Llamar al método bajo prueba
        self.image_message.send_message_to_connector(test_message, test_mobile)
        
        # Verificar que el método no hace nada (comportamiento actual)
        # En una implementación real, aquí irían las aserciones apropiadas
        pass


if __name__ == '__main__':
    unittest.main()
