"""Tests for audio_message module."""
import unittest
from unittest.mock import MagicMock, patch, mock_open

from message.audio_message import AudioMessageInput, AudioMessageOutput, AudioTooLongException


class TestAudioMessageInput(unittest.TestCase):
    """Test cases for AudioMessageInput class."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.mock_conversations = {}
        self.mock_conversations_service = MagicMock()
        self.mock_pipeline = MagicMock()
        self.mock_message_factory = MagicMock()
        self.mock_connector = MagicMock()
        self.mock_message_factory.connector = self.mock_connector
        self.mock_message_factory.data = {}
        self.mock_message_factory.mobile = "+1234567890"
        self.mock_cache_service = MagicMock()
        
        # Crear instancia bajo prueba
        self.audio_message = AudioMessageInput(
            conversations=self.mock_conversations,
            conversations_service=self.mock_conversations_service,
            pipeline=self.mock_pipeline,
            message_factory=self.mock_message_factory,
            cache_service=self.mock_cache_service
        )
        
        # Mock para los métodos de la clase base
        self.audio_message.process_and_run_assistant = MagicMock()
        
    @patch('message.audio_message.speech_to_text')
    def test_handle_success(self, mock_speech_to_text):
        """Test successful audio handling."""
        # Configurar mocks
        test_audio_filename = "test_audio.ogg"
        test_transcription = "This is a test transcription"
        self.mock_connector.get_audio.return_value = test_audio_filename
        mock_speech_to_text.return_value = test_transcription
        
        # Llamar al método bajo prueba
        self.audio_message.handle(stop_bot=False)
        
        # Verificar que se obtuvo el audio y se transcribió
        self.mock_connector.get_audio.assert_called_once_with({})
        mock_speech_to_text.assert_called_once_with(test_audio_filename)
        
        # Verificar que se procesó la transcripción
        self.audio_message.process_and_run_assistant.assert_called_once_with(
            message=test_transcription
        )
    
    @patch('message.audio_message.speech_to_text')
    @patch('message.audio_message.logging')
    def test_handle_audio_too_long(self, mock_logging, mock_speech_to_text):
        """Test handling of audio that's too long."""
        # Configurar mocks
        test_audio_filename = "test_audio.ogg"
        error_message = "Audio is too long"
        self.mock_connector.get_audio.return_value = test_audio_filename
        mock_speech_to_text.side_effect = AudioTooLongException(error_message)
        
        # Llamar al método bajo prueba
        self.audio_message.handle(stop_bot=False)
        
        # Verificar que se manejó el error correctamente
        self.mock_connector.message_type = 'text'
        self.mock_connector.send_message.assert_called_once_with(error_message, self.mock_message_factory.mobile)
        self.audio_message.process_and_run_assistant.assert_not_called()


class TestAudioMessageOutput(unittest.TestCase):
    """Test cases for AudioMessageOutput class."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.mock_conversations = {}
        self.mock_conversations_service = MagicMock()
        self.mock_message_factory = MagicMock()
        self.mock_connector = MagicMock()
        self.mock_message_factory.connector = self.mock_connector
        self.mock_cache_service = MagicMock()
        
        # Configurar el tiempo de finalización de conversación
        from message.audio_message import DEFAULT_FINISH_CONVERSATION_TIME_IN_SECONDS
        self.finish_time = DEFAULT_FINISH_CONVERSATION_TIME_IN_SECONDS
        
        # Crear instancia bajo prueba
        self.audio_message = AudioMessageOutput(
            conversations=self.mock_conversations,
            conversations_service=self.mock_conversations_service,
            message_factory=self.mock_message_factory,
            cache_service=self.mock_cache_service
        )
        
        # Mock para los métodos de la clase base
        self.audio_message.schedule_delete = MagicMock()
    
    @patch('message.audio_message.delete_audio_file')
    @patch('message.audio_message.text_to_speech')
    @patch('builtins.open', new_callable=mock_open)
    @patch('message.audio_message.logging')
    def test_send_message_to_connector_audio_success(
        self, mock_logging, mock_file, mock_text_to_speech, mock_delete_audio_file
    ):
        """Test successful audio message sending with audio generation."""
        # Configurar mocks
        test_message = "Test message"
        test_mobile = "+1234567890"
        test_audio_file = MagicMock()
        test_audio_file.name = "test_audio.mp3"
        test_media_data = {"id": "test_media_id"}
        
        # Configurar el mock para generar audio
        mock_text_to_speech.return_value = test_audio_file
        self.mock_connector.upload_media.return_value = test_media_data
        
        # Forzar la generación de audio
        self.audio_message.generate_audio = lambda: True
        
        # Llamar al método bajo prueba
        self.audio_message.send_message_to_connector(test_message, test_mobile)
        
        # Verificar que se generó el audio
        mock_text_to_speech.assert_called_once_with(test_message)
        
        # Verificar que se subió el audio
        self.mock_connector.upload_media.assert_called_once()
        
        # Verificar que se envió el audio
        self.mock_connector.send_audio.assert_called_once_with(
            test_media_data["id"], test_mobile, False
        )
        
        # Verificar que se eliminó el archivo de audio
        mock_delete_audio_file.assert_called_once_with(test_audio_file.name)
        
        # Verificar que se programó la eliminación
        self.audio_message.schedule_delete.assert_called_once_with(
            test_mobile, self.finish_time
        )
    
    @patch('message.audio_message.logging')
    def test_send_message_to_connector_text_fallback(self, mock_logging):
        """Test fallback to text message when audio generation is disabled."""
        # Configurar mocks
        test_message = "Test message"
        test_mobile = "+1234567890"
        
        # Deshabilitar generación de audio
        self.audio_message.generate_audio = lambda: False
        
        # Llamar al método bajo prueba
        self.audio_message.send_message_to_connector(test_message, test_mobile)
        
        # Verificar que se envió como mensaje de texto
        self.mock_connector.message_type = 'text'
        self.mock_connector.send_message.assert_called_once_with(
            test_message, test_mobile
        )
        
        # Verificar el logging
        mock_logging.info.assert_called_once_with(
            f"Message Response; {test_message}"
        )
        
        # Verificar que se programó la eliminación
        self.audio_message.schedule_delete.assert_called_once()
    
    @patch('message.audio_message.delete_audio_file')
    @patch('message.audio_message.text_to_speech')
    @patch('message.audio_message.logging')
    def test_send_message_to_connector_audio_error(
        self, mock_logging, mock_text_to_speech, mock_delete_audio_file
    ):
        """Test error handling during audio generation."""
        # Configurar mocks
        test_message = "Test message"
        test_mobile = "+1234567890"
        test_error = Exception("Audio generation failed")
        
        # Configurar el mock para fallar
        mock_text_to_speech.side_effect = test_error
        self.audio_message.generate_audio = lambda: True
        
        # Llamar al método bajo prueba
        self.audio_message.send_message_to_connector(test_message, test_mobile)
        
        # Verificar que se manejó el error
        mock_logging.error.assert_called_once_with(
            f"Error during text-to-speech conversion: {str(test_error)}"
        )
        
        # Verificar que se hizo el fallback a mensaje de texto
        self.mock_connector.message_type = 'text'
        self.mock_connector.send_message.assert_called_once_with(
            test_message, test_mobile
        )
        
        # Verificar que se programó la eliminación
        self.audio_message.schedule_delete.assert_called_once_with(
            test_mobile, self.finish_time
        )
