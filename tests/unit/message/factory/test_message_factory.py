"""Tests for message_factory module."""
import unittest
from unittest.mock import MagicMock

from message.factory.message_factory import MessageFactory, get_finish_conversation_time, get_conversation_max_tokens_usage


class TestMessageFactoryUtils(unittest.TestCase):
    """Test cases for utility functions in message_factory module."""
    
    def test_get_finish_conversation_time_default(self):
        """Test get_finish_conversation_time with default value."""
        self.assertEqual(get_finish_conversation_time(None), 1800)
        self.assertEqual(get_finish_conversation_time(0), 1800)
    
    def test_get_finish_conversation_time_custom(self):
        """Test get_finish_conversation_time with custom value."""
        self.assertEqual(get_finish_conversation_time(3600), 3600)
    
    def test_get_conversation_max_tokens_usage_default(self):
        """Test get_conversation_max_tokens_usage with default value."""
        self.assertEqual(get_conversation_max_tokens_usage(None), 100000)
        self.assertEqual(get_conversation_max_tokens_usage(0), 100000)
    
    def test_get_conversation_max_tokens_usage_custom(self):
        """Test get_conversation_max_tokens_usage with custom value."""
        self.assertEqual(get_conversation_max_tokens_usage(200000), 200000)


class TestMessageFactory(unittest.TestCase):
    """Test cases for MessageFactory class."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.connector = MagicMock()
        self.data = {}
        self.mobile = "+1234567890"
        self.name = "Test User"
        self.message_type = "text"
        self.client_data_dto = {
            "_id": "test_client_id",
            "before_instruction_prompt": "Before instruction",
            "after_instruction_prompt": "After instruction",
            "finish_conversation_time": 3600,
            "conversation_max_tokens_usage": 200000,
            "notify_number": "+1987654321",
            "notificate": True,
            "email": "<EMAIL>",
            "notificateToEmail": True,
            "rules": [],
            "assistant_id": "test_assistant_id",
            "finish_conversation_vars": {},
            "integrations": {}
        }
        
        # Create instance under test
        self.factory = MessageFactory(
            connector=self.connector,
            data=self.data,
            mobile=self.mobile,
            name=self.name,
            message_type=self.message_type,
            client_data_dto=self.client_data_dto
        )
    
    def test_initialization(self):
        """Test MessageFactory initialization."""
        # Verify basic attributes
        self.assertEqual(self.factory.connector, self.connector)
        self.assertEqual(self.factory.data, self.data)
        self.assertEqual(self.factory.mobile, self.mobile)
        self.assertEqual(self.factory.name, self.name)
        self.assertEqual(self.factory.message_type, self.message_type)
        self.assertEqual(self.factory.wa_id, self.mobile)
        self.assertEqual(self.factory.client_id, self.client_data_dto["_id"])
        
        # Verify client data attributes
        self.assertEqual(self.factory.notify_number, self.client_data_dto["notify_number"])
        self.assertEqual(self.factory.notificate, self.client_data_dto["notificate"])
        self.assertEqual(self.factory.client_email, self.client_data_dto["email"])
        self.assertEqual(self.factory.notificate_to_email, self.client_data_dto["notificateToEmail"])
        self.assertEqual(self.factory.rules, self.client_data_dto["rules"])
        self.assertEqual(self.factory.assistant_id, self.client_data_dto["assistant_id"])
        self.assertEqual(self.factory.finish_conversation_vars, self.client_data_dto["finish_conversation_vars"])
        self.assertEqual(self.factory.integrations, self.client_data_dto["integrations"])
        
        # Verify calculated attributes
        self.assertEqual(self.factory.hash, f"{self.mobile}{self.client_data_dto['_id']}")
        self.assertEqual(self.factory.finish_conversation_time_in_seconds, 3600)
        self.assertEqual(self.factory.conversation_max_tokens_usage, 200000)
        self.assertEqual(self.factory.before_instruction_prompt, self.client_data_dto["before_instruction_prompt"])
        self.assertEqual(self.factory.after_instruction_prompt, self.client_data_dto["after_instruction_prompt"])
    
    def test_initialization_with_defaults(self):
        """Test MessageFactory initialization with default values."""
        # Create a minimal client_data_dto
        minimal_client_data = {
            "_id": "test_client_id",
            "notify_number": "+1987654321"
        }
        
        # Create instance with minimal data
        factory = MessageFactory(
            connector=self.connector,
            data={},
            mobile="+1234567890",
            name="Test User",
            message_type="text",
            client_data_dto=minimal_client_data
        )
        
        # Verify default values
        self.assertEqual(factory.finish_conversation_time_in_seconds, 1800)  # Default value
        self.assertEqual(factory.conversation_max_tokens_usage, 100000)  # Default value
        self.assertEqual(factory.before_instruction_prompt, '')  # Empty string as default
        self.assertEqual(factory.after_instruction_prompt, '')  # Empty string as default
        self.assertIsNone(factory.assistant_id)  # None as default
        self.assertIsNone(factory.client_email)  # None as default
        self.assertIsNone(factory.notificate)  # None as default
        self.assertIsNone(factory.notificate_to_email)  # None as default
        self.assertIsNone(factory.rules)  # None as default
        self.assertIsNone(factory.finish_conversation_vars)  # None as default
        self.assertIsNone(factory.integrations)  # None as default


if __name__ == '__main__':
    unittest.main()
