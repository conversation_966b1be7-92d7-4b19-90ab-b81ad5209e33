"""Tests for interactive_message module."""
import unittest
from unittest.mock import MagicMock, patch

from message.interactive_message import InteractiveMessageInput, InteractiveMessageOutput


class TestInteractiveMessageInput(unittest.TestCase):
    """Test cases for InteractiveMessageInput class."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.mock_conversations = {}
        self.mock_conversations_service = MagicMock()
        self.mock_pipeline = MagicMock()
        self.mock_message_factory = MagicMock()
        self.mock_connector = MagicMock()
        self.mock_message_factory.connector = self.mock_connector
        self.mock_message_factory.data = {}
        self.mock_cache_service = MagicMock()
        
        # Crear instancia bajo prueba
        self.interactive_message = InteractiveMessageInput(
            conversations=self.mock_conversations,
            conversations_service=self.mock_conversations_service,
            pipeline=self.mock_pipeline,
            message_factory=self.mock_message_factory,
            cache_service=self.mock_cache_service
        )
        
        # Configurar el mock para get_interactive_response
        self.test_interactive_data = {
            "type": "button_reply",
            "button_reply": {
                "id": "button_1",
                "title": "Option 1"
            }
        }
        self.mock_connector.get_interactive_response.return_value = self.test_interactive_data
    
    @patch('message.interactive_message.logging')
    def test_handle_interactive_processing(self, mock_logging):
        """Test successful interactive message processing."""
        # Llamar al método bajo prueba
        self.interactive_message.handle(stop_bot=False)
        
        # Verificar que se obtuvo la respuesta interactiva
        self.mock_connector.get_interactive_response.assert_called_once_with({})
        
        # Verificar el registro de log
        mock_logging.info.assert_called_once_with(
            f"Interactive Message; {self.test_interactive_data['button_reply']['id']}: "
            f"{self.test_interactive_data['button_reply']['title']}"
        )
    
    @patch('message.interactive_message.logging')
    def test_handle_different_interactive_type(self, mock_logging):
        """Test handling of different interactive message types."""
        # Configurar un tipo de mensaje interactivo diferente
        self.test_interactive_data = {
            "type": "list_reply",
            "list_reply": {
                "id": "list_1",
                "title": "List Option 1"
            }
        }
        self.mock_connector.get_interactive_response.return_value = self.test_interactive_data
        
        # Llamar al método bajo prueba
        self.interactive_message.handle(stop_bot=False)
        
        # Verificar el registro de log con el nuevo tipo
        mock_logging.info.assert_called_once_with(
            f"Interactive Message; {self.test_interactive_data['list_reply']['id']}: "
            f"{self.test_interactive_data['list_reply']['title']}"
        )


class TestInteractiveMessageOutput(unittest.TestCase):
    """Test cases for InteractiveMessageOutput class."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.mock_conversations = {}
        self.mock_conversations_service = MagicMock()
        self.mock_message_factory = MagicMock()
        self.mock_connector = MagicMock()
        self.mock_message_factory.connector = self.mock_connector
        self.mock_cache_service = MagicMock()
        
        # Crear instancia bajo prueba
        self.interactive_message = InteractiveMessageOutput(
            conversations=self.mock_conversations,
            conversations_service=self.mock_conversations_service,
            message_factory=self.mock_message_factory,
            cache_service=self.mock_cache_service
        )
    
    def test_send_message_to_connector(self):
        """Test placeholder for send_message_to_connector."""
        # Este es un método placeholder que no hace nada en la implementación actual
        test_message = "Test message"
        test_mobile = "+1234567890"
        
        # Llamar al método bajo prueba
        self.interactive_message.send_message_to_connector(test_message, test_mobile)
        
        # Verificar que el método no hace nada (comportamiento actual)
        # En una implementación real, aquí irían las aserciones apropiadas
        pass


if __name__ == '__main__':
    unittest.main()
