"""Tests for base_message module."""
import unittest
from unittest.mock import MagicM<PERSON>, patch

from enums.message_types import RoleMessageType
from events.event_types import RECEIVED_MESSAGE
from message.base_message import BaseMessageInput, BaseMessageOutput

# Constants for testing
TEST_HASH = 'test_hash_123'
TEST_ASSISTANT_ID = 'test_assistant_456'
TEST_THREAD_ID = 'test_thread_789'
TEST_CONVERSATION_ID = 'test_conv_012'
TEST_MOBILE = '+1234567890'
TEST_NAME = 'Test User'
TEST_MESSAGE = 'Hello, this is a test message'

class MockConversationsService:
    """Mock service for conversations."""
    
    def __init__(self):
        """Initialize mock service with default values."""
        self.get_conversation_by_hash = MagicMock()
        self.update_chat_history_in_conversation = MagicMock()
        self.add_message_to_conversation = MagicMock()
        
        # Setup a default conversation database mock
        self.conversation_db = MagicMock()
        self.conversation_db.botstop = False
        self.conversation_db.assign = 'test_assign'
        self.conversation_db.id = 'test_lead_id'
        self.conversation_db.lead_name = 'Test Lead'
        
        # Configure get_conversation_by_hash to return the mock by default
        self.get_conversation_by_hash.return_value = self.conversation_db


class MockCacheService:
    """Mock service for cache operations."""
    
    def __init__(self):
        """Initialize mock cache with default values."""
        self.get_cache_value = MagicMock()
        self.set_cache_value = MagicMock()
        self.delete_cache_value = MagicMock()
        
        # Configure default cache data
        self.cache_data = {
            'thread_id': TEST_THREAD_ID,
            'conversation_id': TEST_CONVERSATION_ID
        }
        self.get_cache_value.return_value = self.cache_data


class MockMessageFactory:
    """Mock factory for creating message-related objects."""
    
    def __init__(self):
        """Initialize mock factory with default values."""
        self.connector = MagicMock()
        self.hash = TEST_HASH
        self.assistant_id = TEST_ASSISTANT_ID
        self.name = TEST_NAME
        self.mobile = TEST_MOBILE
        self.before_instruction_prompt = ''
        self.after_instruction_prompt = ''
        self.conversation_max_tokens_usage = 1000

class TestBaseMessageInput(unittest.TestCase):
    """Test cases for BaseMessageInput class."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        # Initialize mock services
        self.mock_conversations = {}
        self.mock_conversations_service = MockConversationsService()
        self.mock_message_factory = MockMessageFactory()
        self.mock_cache_service = MockCacheService()
        self.mock_pipeline = MagicMock()
        
        # Create instance with mocks
        self.base_message = BaseMessageInput(
            conversations=self.mock_conversations,
            conversations_service=self.mock_conversations_service,
            pipeline=self.mock_pipeline,
            message_factory=self.mock_message_factory,
            cache_service=self.mock_cache_service
        )
        
        # Common test data
        self.test_conversation = {
            'timer': None,
            'thread_id': TEST_THREAD_ID,
            'update_conversation': True,
            'conversation_id': TEST_CONVERSATION_ID,
            'token_usage': 0
        }
    
    @patch('message.base_message.AssistantManager')
    def test_create_new_thread(self, mock_assistant_manager):
        # Configurar mocks
        mock_assistant = MagicMock()
        mock_thread = MagicMock()
        mock_thread.id = "test_thread_id"
        mock_assistant.create_thread.return_value = mock_thread
        mock_assistant_manager.get_assistant_by_id.return_value = mock_assistant
        
        # Configurar datos de prueba
        self.mock_message_factory.name = "Test User"
        self.mock_message_factory.mobile = "+1234567890"
        
        # Ejecutar método bajo prueba
        thread_id, conversation_id = self.base_message._create_new_thread(mock_assistant)
        
        # Verificar resultados
        self.assertEqual(thread_id, "test_thread_id")
        self.assertEqual(conversation_id, "")
        mock_assistant.create_thread.assert_called_once()
        mock_assistant.create_user_message.assert_called_once_with(
            thread_id="test_thread_id",
            message_content="Mi nombre es Test User y mi telefono es +1234567890",
            role=RoleMessageType.USER
        )
        self.mock_cache_service.set_cache_value.assert_called_once_with(
            key="test_hash",
            thread_id="test_thread_id",
            conversation_id=""
        )
    
    def test_cancel_existing_timer_no_timer(self):
        # Configurar datos de prueba sin timer
        self.mock_conversations[self.base_message.hash] = {'timer': None}
    
        # Ejecutar método bajo prueba
        self.base_message.cancel_existing_timer()
        
        # Verificar que no se intentó cancelar ningún timer
        # (no hay nada que verificar ya que no debería hacer nada)
        
        # Verificar que no se llamó a cancel()
        self.assertIsNone(self.mock_conversations[self.base_message.hash]['timer'])
    
    def test_cancel_existing_timer_with_timer(self):
        # Configurar mocks
        mock_timer = MagicMock()
        self.mock_conversations[self.base_message.hash] = {'timer': mock_timer}
    
        # Ejecutar método bajo prueba
        self.base_message.cancel_existing_timer()
        
        # Verificar que se llamó a cancel en el timer
        mock_timer.cancel.assert_called_once()
        
        # Verificar que se eliminó el timer de la conversación
        self.assertIsNone(self.mock_conversations[self.base_message.hash]['timer'])
        
        # Verificar que se llamó a cancel() y se estableció a None
        mock_timer.cancel.assert_called_once()
        self.assertIsNone(self.mock_conversations[self.base_message.hash]['timer'])
    
    @patch('message.base_message.send_event')
    @patch('message.base_message.RoleMessageType')
    def test_add_message_to_chathistory(self, mock_role_message_type, mock_send_event):
        # Configurar mocks
        mock_role_message_type.USER.to_string.return_value = "user"
        
        # Configurar datos de prueba
        message = "Test message"
        conversation_id = "test_conv_id"
        
        # Configurar el mock para conversations_service
        self.mock_conversations_service.update_chat_history_in_conversation = MagicMock(return_value=None)
        
        # Configurar el hash en el message_factory
        self.base_message.hash = "test_hash"
        
        # Configurar la conversación en self.base_message.conversations
        self.base_message.conversations = {
            "test_hash": {
                "conversation_id": conversation_id,
                "mobile": "+1234567890"
            }
        }
        
        # Ejecutar método bajo prueba
        result = self.base_message.add_message_to_chathistory(message, conversation_id)
        
        # Verificar que se llamó a los métodos correctos
        self.mock_conversations_service.update_chat_history_in_conversation.assert_called_once_with(
            conversation_id=conversation_id,
            new_message={
                "role": "user",
                "content": message
            }
        )
        
        # Verificar que se devolvió None
        self.assertIsNone(result)
    
    @patch('message.base_message.send_event')
    @patch('message.base_message.logging')
    def test_send_notification_new_message_to_crm(self, mock_logging, mock_send_event):
        # Configurar datos de prueba
        test_message = "Test message"
        
        # Configurar el conversation_db
        self.base_message.conversation_db = MagicMock()
        self.base_message.conversation_db.assign = "test_assign"
        self.base_message.conversation_db.id = "test_lead_id"
        self.base_message.conversation_db.lead_name = "Test Lead"
        
        # Ejecutar método bajo prueba
        self.base_message.send_notification_new_message_to_crm(test_message)
        
        # Verificar que se llamó a send_event con los parámetros correctos
        mock_send_event.assert_called_once_with(
            event_type=RECEIVED_MESSAGE,
            payload={
                "payload": {
                    "assign": "test_assign",
                    "leadId": "test_lead_id",
                    "senderName": "Test Lead",
                    "content": test_message
                }
            }
        )
        
        # Verificar que se registró el mensaje de log
        mock_logging.info.assert_called_once_with("Send crm new message notification")
    
    @patch('message.base_message.AssistantManager.get_assistant_by_id')
    def test_initialize_conversation_with_botstop(self, mock_get_assistant):
        # Configurar mocks
        mock_conversation_db = MagicMock()
        mock_conversation_db.botstop = True
        self.mock_conversations_service.get_conversation_by_hash.return_value = mock_conversation_db
        
        # Configurar el message_factory
        self.base_message.message_factory.assistant_id = 'test_assistant_id'
        
        # Configurar el mock para get_assistant_by_id
        mock_assistant = MagicMock()
        mock_get_assistant.return_value = mock_assistant
        
        # Ejecutar método bajo prueba
        result = self.base_message.initialize_conversation()
        
        # Verificar que se devolvió True
        self.assertTrue(result)
        
        # Verificar que se llamó a get_assistant_by_id
        mock_get_assistant.assert_called_once_with('test_assistant_id')
        
        # Verificar que no se intentó crear un nuevo hilo
        mock_assistant.create_thread.assert_not_called()
    
    @patch('message.base_message.AssistantManager')
    @patch('message.base_message.BaseMessageInput._create_new_thread')
    def test_initialize_conversation_with_cache(self, mock_create_thread, mock_assistant_manager):
        # Configurar mocks
        mock_conversation_db = MagicMock()
        mock_conversation_db.botstop = False
        self.mock_conversations_service.get_conversation_by_hash.return_value = mock_conversation_db
        
        # Configurar datos de caché
        cache_data = {
            'thread_id': 'cached_thread_id',
            'conversation_id': 'cached_conv_id'
        }
        self.base_message.cache_service.get_cache_value.return_value = cache_data
        
        # Ejecutar método bajo prueba
        result = self.base_message.initialize_conversation()
        
        # Verificar que se devolvió False
        self.assertFalse(result)
        
        # Verificar que se usó el hilo en caché
        mock_create_thread.assert_not_called()
        
        # Verificar que se configuró la conversación correctamente
        self.assertEqual(self.base_message.conversations[self.base_message.hash]['thread_id'], 'cached_thread_id')
        self.assertEqual(self.base_message.conversations[self.base_message.hash]['conversation_id'], 'cached_conv_id')
        self.assertTrue(self.base_message.conversations[self.base_message.hash]['update_conversation'])
    
    @patch('message.base_message.AssistantManager')
    @patch('message.base_message.BaseMessageInput._create_new_thread')
    def test_initialize_conversation_without_cache(self, mock_create_thread, mock_assistant_manager):
        # Configurar mocks
        mock_conversation_db = MagicMock()
        mock_conversation_db.botstop = False
        self.mock_conversations_service.get_conversation_by_hash.return_value = mock_conversation_db
        
        # Configurar sin caché
        self.base_message.cache_service.get_cache_value.return_value = None
        
        # Configurar el mock de _create_new_thread
        mock_create_thread.return_value = ('new_thread_id', 'new_conv_id')
        
        # Configurar el asistente
        mock_assistant = MagicMock()
        mock_assistant_manager.get_assistant_by_id.return_value = mock_assistant
        
        # Ejecutar método bajo prueba
        result = self.base_message.initialize_conversation()
        
        # Verificar que se devolvió False
        self.assertFalse(result)
        
        # Verificar que se creó un nuevo hilo
        mock_create_thread.assert_called_once_with(mock_assistant)
        
        # Verificar que se configuró la conversación correctamente
        self.assertEqual(self.base_message.conversations[self.base_message.hash]['thread_id'], 'new_thread_id')
        self.assertEqual(self.base_message.conversations[self.base_message.hash]['conversation_id'], 'new_conv_id')
    
    def test_handle_not_implemented(self):
        """Test that handle method raises NotImplementedError."""
        with self.assertRaises(NotImplementedError):
            self.base_message.handle(stop_bot=False)
    
    def test_process_and_run_assistant(self):
        """Test process_and_run_assistant method."""
        # Setup
        test_message = "Test message"
        expected_message = f"{self.mock_message_factory.before_instruction_prompt}{test_message}{self.mock_message_factory.after_instruction_prompt}"
        
        with patch.object(self.base_message, 'run_assistant') as mock_run_assistant:
            # Execute
            self.base_message.process_and_run_assistant(test_message)
            
            # Verify
            mock_run_assistant.assert_called_once_with(message=expected_message)
    
    def test_add_message_to_chathistory(self):
        """Test add_message_to_chathistory method."""
        # Setup
        test_message = "Test message"
        test_conversation_id = "test_conv_123"
        expected_message = {"role": RoleMessageType.USER.to_string(), "content": test_message}
        
        # Execute
        result = self.base_message.add_message_to_chathistory(
            message=test_message,
            conversation_id=test_conversation_id
        )
        
        # Verify
        self.mock_conversations_service.update_chat_history_in_conversation.assert_called_once_with(
            conversation_id=test_conversation_id,
            new_message=expected_message
        )
        self.assertIsNone(result)
    
    @patch('message.base_message.logging')
    def test_send_notification_new_message_to_crm(self, mock_logging):
        """Test send_notification_new_message_to_crm method."""
        # Setup
        test_message = "Test message"
        
        # Create a real instance with conversation_db set
        self.base_message.conversation_db = self.mock_conversations_service.conversation_db
        
        with patch('message.base_message.send_event') as mock_send_event:
            # Execute
            self.base_message.send_notification_new_message_to_crm(test_message)
            
            # Verify event was sent with correct data
            expected_payload = {
                "assign": self.mock_conversations_service.conversation_db.assign,
                "leadId": self.mock_conversations_service.conversation_db.id,
                "senderName": self.mock_conversations_service.conversation_db.lead_name,
                "content": test_message,
            }
            mock_send_event.assert_called_once_with(
                event_type=RECEIVED_MESSAGE,
                payload={"payload": expected_payload}
            )
            
            # Verify logging
            mock_logging.info.assert_called_once_with("Send crm new message notification")
    
    @patch('message.base_message.AssistantManager')
    def test_create_new_thread(self, mock_assistant_manager):
        """Test _create_new_thread method."""
        # Setup mocks
        mock_assistant = MagicMock()
        mock_thread = MagicMock()
        mock_thread.id = TEST_THREAD_ID
        mock_assistant.create_thread.return_value = mock_thread
        mock_assistant_manager.get_assistant_by_id.return_value = mock_assistant
        
        # Configure message_factory
        self.base_message.message_factory.name = TEST_NAME
        self.base_message.message_factory.mobile = TEST_MOBILE
        
        # Configure cache service mock
        mock_cache_set = MagicMock()
        self.base_message.cache_service.set_cache_value = mock_cache_set
        
        # Execute method under test
        thread_id, conversation_id = self.base_message._create_new_thread(mock_assistant)
        
        # Verify thread was created
        mock_assistant.create_thread.assert_called_once()
        
        # Verify user message was created
        expected_message = f'Mi nombre es {TEST_NAME} y mi telefono es {TEST_MOBILE}'
        mock_assistant.create_user_message.assert_called_once_with(
            thread_id=TEST_THREAD_ID,
            message_content=expected_message,
            role=RoleMessageType.USER
        )
        
        # Verify cache was updated
        mock_cache_set.assert_called_once_with(
            key=self.base_message.hash,
            thread_id=TEST_THREAD_ID,
            conversation_id=''
        )
        
        # Verify return values
        self.assertEqual(thread_id, TEST_THREAD_ID)
        self.assertEqual(conversation_id, '')
    
    @patch('message.base_message.AssistantManager.get_assistant_by_id')
    @patch('message.base_message.logging')
    def test_delete_conversation(self, mock_logging, mock_get_assistant):
        """Test delete_conversation method."""
        # Setup mocks
        self.base_message.conversations = {self.base_message.hash: {'thread_id': 'test_thread'}}
        
        # Execute method under test
        self.base_message.delete_conversation()
        
        # Verify conversation was deleted
        self.assertNotIn(self.base_message.hash, self.base_message.conversations)
        
        # Verify cache was cleared
        self.base_message.cache_service.delete_cache_value.assert_called_once_with(
            key=self.base_message.hash
        )
        
        # Verify logging
        mock_logging.info.assert_called_once_with(
            f"Deleted conversation for mobile {self.base_message.hash}"
        )
        
        # Test when conversation doesn't exist
        self.base_message.conversations = {}
        self.base_message.cache_service.delete_cache_value.reset_mock()
        
        # Execute method under test
        self.base_message.delete_conversation()
        
        # Verify cache was not cleared
        self.base_message.cache_service.delete_cache_value.assert_not_called()
        
        # Verify logging
        mock_logging.info.assert_called_with(
            f"No conversation found for mobile {self.base_message.hash}"
        )
    
    @patch('message.base_message.logging')
    def test_delete_conversation_with_error(self, mock_logging):
        """Test delete_conversation method with error."""
        # Setup mocks to raise an exception
        self.base_message.conversations = {self.base_message.hash: {'thread_id': 'test_thread'}}
        self.base_message.cache_service.delete_cache_value.side_effect = Exception("Test error")
        
        # Execute method under test
        self.base_message.delete_conversation()
        
        # Verify error was logged
        mock_logging.error.assert_called_once()
        self.assertIn("Error in generate lead for mobile", mock_logging.error.call_args[0][0])
    
    @patch('message.base_message.AssistantManager')
    @patch('message.base_message.logging')
    def test_run_assistant(self, mock_logging, mock_assistant_manager):
        """Test run_assistant method."""
        # Setup mocks
        mock_assistant = MagicMock()
        mock_assistant_manager.get_assistant_by_id.return_value = mock_assistant
        
        # Setup test conversation
        self.base_message.conversations = {
            self.base_message.hash: {
                'thread_id': TEST_THREAD_ID,
                'token_usage': 0,
                'update_conversation': True,
                'conversation_id': TEST_CONVERSATION_ID
            }
        }
        
        # Execute method under test
        test_message = "Test message"
        self.base_message.run_assistant(test_message)
        
        # Verify assistant was called correctly
        mock_assistant.create_user_message.assert_called_once_with(
            thread_id=TEST_THREAD_ID,
            message_content=test_message,
            role=RoleMessageType.USER
        )
        
        mock_assistant.run_assistant_with_stream.assert_called_once_with(
            thread_id=TEST_THREAD_ID,
            message_factory=self.base_message.message_factory,
            conversations=self.base_message.conversations,
            pipeline=self.base_message.pipeline
        )
        
        # Test with token limit exceeded
        self.base_message.conversations[self.base_message.hash]['token_usage'] = 2000
        self.base_message.message_factory.conversation_max_tokens_usage = 1000
        
        # Reset mocks
        mock_assistant.create_user_message.reset_mock()
        mock_assistant.run_assistant_with_stream.reset_mock()
        
        # Execute method under test
        self.base_message.run_assistant(test_message)
        
        # Verify assistant was not called
        mock_assistant.create_user_message.assert_not_called()
        mock_assistant.run_assistant_with_stream.assert_not_called()
        
        # Test with exception
        self.base_message.conversations[self.base_message.hash]['token_usage'] = 0
        mock_assistant.create_user_message.side_effect = Exception("Test error")
        
        # Execute method under test
        self.base_message.run_assistant(test_message)
        
        # Verify error was logged
        mock_logging.error.assert_called_once_with(
            "Error occurred while processing message: %s", "Test error"
        )
    
    @patch('message.base_message.LeadManager')
    def test_delete_conversation_and_generate_lead(self, mock_lead_manager_class):
        """Test delete_conversation_and_generate_lead method."""
        # Setup mocks
        mock_lead_manager = MagicMock()
        mock_lead_manager_class.return_value = mock_lead_manager
        
        # Setup test conversation
        test_conversation = {
            'update_conversation': True,
            'conversation_id': TEST_CONVERSATION_ID
        }
        self.base_message.conversations = {self.base_message.hash: test_conversation}
        
        # Execute method under test
        self.base_message.delete_conversation_and_generate_lead()
        
        # Verify lead manager was called with correct arguments
        mock_lead_manager_class.assert_called_once_with(
            message_factory=self.base_message.message_factory,
            conversations=self.base_message.conversations,
            conversations_service=self.base_message.conversations_service,
            connector=self.base_message.connector,
            cache_service=self.base_message.cache_service
        )
        
        # Verify generate_or_update_lead was called with update_conversation
        mock_lead_manager.generate_or_update_lead.assert_called_once_with(True)
        
        # Verify conversation was deleted
        self.assertNotIn(self.base_message.hash, self.base_message.conversations)
        
        # Test when conversation doesn't exist
        self.base_message.conversations = {}
        mock_lead_manager_class.reset_mock()
        
        # Execute method under test
        self.base_message.delete_conversation_and_generate_lead()
        
        # Verify lead manager was not called
        mock_lead_manager_class.assert_not_called()
        
    @patch('message.base_message.logging')
    def test_delete_conversation_and_generate_lead_with_error(self, mock_logging):
        """Test delete_conversation_and_generate_lead method with error."""
        # Setup mocks to raise an exception
        test_conversation = {
            'update_conversation': True,
            'conversation_id': TEST_CONVERSATION_ID
        }
        self.base_message.conversations = {self.base_message.hash: test_conversation}
        
        with patch('message.base_message.LeadManager') as mock_lead_manager_class:
            mock_lead_manager = MagicMock()
            mock_lead_manager_class.return_value = mock_lead_manager
            mock_lead_manager.generate_or_update_lead.side_effect = Exception("Test error")
            
            # Execute method under test
            self.base_message.delete_conversation_and_generate_lead()
            
            # Verify error was logged
            mock_logging.error.assert_called_once()
            self.assertIn("Error in generate lead for mobile", mock_logging.error.call_args[0][0])
            
            # Verify conversation was still deleted
            self.assertNotIn(self.base_message.hash, self.base_message.conversations)
    
    @patch('message.base_message.AssistantManager')
    @patch('message.base_message.logging')
    def test_run_assistant(self, mock_logging, mock_assistant_manager):
        """Test run_assistant method with various scenarios."""
        # Setup mocks
        mock_assistant = MagicMock()
        mock_assistant_manager.get_assistant_by_id.return_value = mock_assistant
        
        # Setup test conversation
        test_conversation = {
            'thread_id': 'test_thread',
            'token_usage': 0,
            'update_conversation': True,
            'conversation_id': 'test_conv_123'
        }
        self.base_message.conversations = {self.base_message.hash: test_conversation}
        
        # Configure message_factory
        self.base_message.message_factory.conversation_max_tokens_usage = 1000
        
        # Test normal execution
        test_message = "Test message"
        self.base_message.run_assistant(test_message)
        
        # Verify assistant was called correctly
        mock_assistant.create_user_message.assert_called_once_with(
            thread_id='test_thread',
            message_content=test_message,
            role=RoleMessageType.USER
        )
        
        mock_assistant.run_assistant_with_stream.assert_called_once_with(
            thread_id='test_thread',
            message_factory=self.base_message.message_factory,
            conversations=self.base_message.conversations,
            pipeline=self.base_message.pipeline
        )
        
        # Test with token limit exceeded
        self.base_message.conversations[self.base_message.hash]['token_usage'] = 2000
        mock_assistant.create_user_message.reset_mock()
        mock_assistant.run_assistant_with_stream.reset_mock()
        
        self.base_message.run_assistant(test_message)
        
        # Verify assistant was not called due to token limit
        mock_assistant.create_user_message.assert_not_called()
        mock_assistant.run_assistant_with_stream.assert_not_called()
        
        # Test with exception
        self.base_message.conversations[self.base_message.hash]['token_usage'] = 0
        mock_assistant.create_user_message.side_effect = Exception("Test error")
        
        self.base_message.run_assistant(test_message)
        
        # Verify error was logged
        mock_logging.error.assert_called_once_with(
            "Error occurred while processing message: %s", "Test error"
        )

class TestBaseMessageOutput(unittest.TestCase):
    def setUp(self):
        # Configurar mocks
        self.mock_conversations = {}
        self.mock_message_factory = MockMessageFactory()
        self.mock_conversations_service = MockConversationsService()
        self.mock_cache_service = MockCacheService()
        
        # Crear instancia de BaseMessageOutput con los mocks
        self.base_message_output = BaseMessageOutput(
            conversations=self.mock_conversations,
            conversations_service=self.mock_conversations_service,
            message_factory=self.mock_message_factory,
            cache_service=self.mock_cache_service
        )
    
    @patch('message.base_message.LeadManager')
    def test_send_message_to_connector(self, mock_lead_manager):
        # Crear una implementación concreta de BaseMessageOutput
        class TestMessageOutput(BaseMessageOutput):
            def send_message_to_connector(self, message, mobile):
                self.connector.send_message(message=message, mobile=mobile)
        
        # Configurar datos de prueba
        message = "Test message"
        mobile = "+1234567890"
        
        # Configurar el mock del conector
        connector_mock = MagicMock()
        self.mock_message_factory.connector = connector_mock
        
        # Crear instancia de la implementación concreta
        output = TestMessageOutput(
            conversations=self.mock_conversations,
            conversations_service=self.mock_conversations_service,
            message_factory=self.mock_message_factory,
            cache_service=self.mock_cache_service
        )
        
        # Ejecutar método bajo prueba
        output.send_message_to_connector(message, mobile)
        
        # Verificar que se llamó al conector correctamente
        connector_mock.send_message.assert_called_once_with(
            message=message,
            mobile=mobile
        )
    
    @patch('message.base_message.threading.Timer')
    @patch('message.base_message.logging')
    def test_schedule_delete(self, mock_logging, mock_timer):
        # Configurar mocks
        mock_timer_instance = MagicMock()
        mock_timer.return_value = mock_timer_instance
    
        # Configurar datos de prueba
        mobile = "+1234567890"
        delay = 60
        
        # Configurar el hash en el message_factory
        test_hash = "test_hash_123"
        self.base_message_output.message_factory.hash = test_hash
        self.base_message_output.hash = test_hash
    
        # Configurar la conversación
        self.mock_conversations = {}
        self.base_message_output.conversations = self.mock_conversations
        
        # Configurar la conversación con el timer
        self.mock_conversations[test_hash] = {
            'mobile': mobile,
            'timer': None,
            'update_conversation': MagicMock(),
            'conversation_id': 'test_conversation_id'
        }
    
        # Ejecutar método bajo prueba
        self.base_message_output.schedule_delete(mobile, delay)
    
        # Verificar que se creó el timer con los argumentos correctos
        mock_timer.assert_called_once_with(
            delay,
            self.base_message_output.delete_conversation_and_generate_lead
        )
        
        # Verificar que se guardó el timer en la conversación
        self.assertEqual(
            self.mock_conversations[test_hash]['timer'],
            mock_timer_instance
        )
        
        # Verificar que se llamó a start() en el timer
        mock_timer_instance.start.assert_called_once()
        
        # Verificar que se registró el mensaje de programación
        mock_logging.info.assert_called_with(
            f"Scheduled conversation deletion for mobile {mobile} in {delay} seconds"
        )
    
    @patch('message.base_message.threading.Timer')
    @patch('message.base_message.logging')
    def test_schedule_delete_no_conversation(self, mock_logging, mock_timer):
        # Configurar datos de prueba
        mobile = "+1234567890"
        delay = 60
        
        # Configurar el hash en el message_factory
        test_hash = "non_existent_hash"
        self.base_message_output.message_factory.hash = test_hash
        self.base_message_output.hash = test_hash
        
        # Configurar conversación vacía
        self.mock_conversations = {}
        self.base_message_output.conversations = self.mock_conversations
        
        # Ejecutar método bajo prueba
        self.base_message_output.schedule_delete(mobile, delay)
        
        # Verificar que no se creó ningún timer
        mock_timer.assert_not_called()
        
        # Verificar que se registró el mensaje de conversación no encontrada
        mock_logging.info.assert_called_with(
            f"No conversation found for mobile {mobile} or timer already exists"
        )
    
    @patch('message.base_message.threading.Timer')
    @patch('message.base_message.logging')
    def test_schedule_delete_timer_exists(self, mock_logging, mock_timer):
        # Configurar datos de prueba
        mobile = "+1234567890"
        delay = 60
        
        # Configurar el hash en el message_factory
        test_hash = "test_hash_with_timer"
        self.base_message_output.message_factory.hash = test_hash
        self.base_message_output.hash = test_hash
        
        # Configurar la conversación con un timer existente
        existing_timer = MagicMock()
        self.mock_conversations = {
            test_hash: {
                'mobile': mobile,
                'timer': existing_timer,  # Timer existente
                'update_conversation': MagicMock(),
                'conversation_id': 'test_conversation_id_2'
            }
        }
        self.base_message_output.conversations = self.mock_conversations
        
        # Ejecutar método bajo prueba
        self.base_message_output.schedule_delete(mobile, delay)
        
        # Verificar que no se creó ningún nuevo timer
        mock_timer.assert_not_called()
        
        # Verificar que se registró el mensaje de que ya existe un timer
        mock_logging.info.assert_called_with(
            f"No conversation found for mobile {mobile} or timer already exists"
        )
    
    @patch('message.base_message.LeadManager')
    @patch('message.base_message.logging')
    @patch('message.base_message.GENERATE_LEAD_ERROR')
    def test_delete_conversation_and_generate_lead(self, mock_generate_lead_error, mock_logging, mock_lead_manager_class):
        # Configurar mocks
        mock_lead_manager = MagicMock()
        mock_lead_manager_class.return_value = mock_lead_manager
        
        # Configurar datos de prueba
        test_hash = "test_hash_123"
        self.base_message_output.hash = test_hash
        update_conversation_mock = MagicMock()
        
        # Configurar la conversación
        self.mock_conversations = {
            test_hash: {
                'update_conversation': update_conversation_mock,
                'mobile': '+1234567890'
            }
        }
        self.base_message_output.conversations = self.mock_conversations
        
        # Ejecutar método bajo prueba
        self.base_message_output.delete_conversation_and_generate_lead()
        
        # Verificar que se creó el LeadManager con los parámetros correctos
        mock_lead_manager_class.assert_called_once_with(
            message_factory=self.base_message_output.message_factory,
            conversations=self.mock_conversations,
            conversations_service=self.base_message_output.conversations_service,
            connector=self.base_message_output.connector,
            cache_service=self.base_message_output.cache_service
        )
        
        # Verificar que se llamó a generate_or_update_lead
        mock_lead_manager.generate_or_update_lead.assert_called_once_with(update_conversation_mock)
        
        # Verificar que se eliminó la conversación
        self.assertNotIn(test_hash, self.mock_conversations)
        
        # Verificar que se registró el mensaje de eliminación
        mock_logging.info.assert_called_with(f"Deleted conversation for mobile {test_hash}")
    
    @patch('message.base_message.LeadManager')
    @patch('message.base_message.logging')
    @patch('message.base_message.GENERATE_LEAD_ERROR')
    def test_delete_conversation_and_generate_lead_with_error(self, mock_generate_lead_error, mock_logging, mock_lead_manager_class):
        # Configurar mocks
        mock_lead_manager = MagicMock()
        mock_lead_manager.generate_or_update_lead.side_effect = Exception("Test error")
        mock_lead_manager_class.return_value = mock_lead_manager
        
        # Configurar datos de prueba
        test_hash = "test_hash_error"
        self.base_message_output.hash = test_hash
        update_conversation_mock = MagicMock()
        
        # Configurar la conversación
        self.mock_conversations = {
            test_hash: {
                'update_conversation': update_conversation_mock,
                'mobile': '+1234567890'
            }
        }
        self.base_message_output.conversations = self.mock_conversations
        
        # Ejecutar método bajo prueba
        self.base_message_output.delete_conversation_and_generate_lead()
        
        # Verificar que se incrementó el contador de errores
        mock_generate_lead_error.inc.assert_called_once()
        
        # Verificar que se registró el error
        mock_logging.error.assert_called_once_with(
            f"Error in generate lead for mobile {test_hash}: Test error\n"
        )
        
        # Verificar que se eliminó la conversación a pesar del error
        self.assertNotIn(test_hash, self.mock_conversations)
    
    @patch('message.base_message.logging')
    def test_delete_conversation_and_generate_lead_no_conversation(self, mock_logging):
        # Configurar datos de prueba
        test_hash = "non_existent_hash"
        self.base_message_output.hash = test_hash
        self.mock_conversations = {}
        self.base_message_output.conversations = self.mock_conversations
        
        # Ejecutar método bajo prueba
        self.base_message_output.delete_conversation_and_generate_lead()
        
        # Verificar que se registró el mensaje de conversación no encontrada
        mock_logging.info.assert_called_once_with(
            f"No conversation found for mobile {test_hash}"
        )
    
    @patch('message.base_message.LeadManager')
    @patch('message.base_message.logging')
    @patch('message.base_message.GENERATE_LEAD_ERROR')
    def test_delete_conversation_and_generate_lead_with_error_and_no_conversation(self, mock_generate_lead_error, mock_logging, mock_lead_manager_class):
        # Configurar mocks
        mock_lead_manager = MagicMock()
        mock_lead_manager.generate_or_update_lead.side_effect = Exception("Test error")
        mock_lead_manager_class.return_value = mock_lead_manager
        
        # Configurar datos de prueba
        test_hash = "non_existent_hash"
        self.base_message_output.hash = test_hash
        self.mock_conversations = {}
        self.base_message_output.conversations = self.mock_conversations
        
        # Ejecutar método bajo prueba
        self.base_message_output.delete_conversation_and_generate_lead()
        
        # Verificar que se registró el mensaje de conversación no encontrada
        mock_logging.info.assert_called_once_with(
            f"No conversation found for mobile {test_hash}"
        )
        
        # Verificar que no se incrementó el contador de errores
        mock_generate_lead_error.inc.assert_not_called()
    
    @patch('message.base_message.LeadManager')
    @patch('message.base_message.logging')
    @patch('message.base_message.GENERATE_LEAD_ERROR')
    def test_delete_conversation_and_generate_lead_with_empty_conversation(self, mock_generate_lead_error, mock_logging, mock_lead_manager_class):
        # Configurar mocks
        mock_lead_manager = MagicMock()
        mock_lead_manager.generate_or_update_lead.side_effect = Exception("Test error")
        mock_lead_manager_class.return_value = mock_lead_manager
        
        # Configurar datos de prueba
        test_hash = "test_hash_empty"
        self.base_message_output.hash = test_hash
        self.mock_conversations = {
            test_hash: {}
        }
        self.base_message_output.conversations = self.mock_conversations
        
        # Ejecutar método bajo prueba
        self.base_message_output.delete_conversation_and_generate_lead()
        
        # Verificar que se registró el mensaje de eliminación
        mock_logging.info.assert_called_with(
            f"Deleted conversation for mobile {test_hash}"
        )
        
        # Verificar que se incrementó el contador de errores
        mock_generate_lead_error.inc.assert_called_once()
        
        # Verificar que se eliminó la conversación
        self.assertNotIn(test_hash, self.mock_conversations)

if __name__ == '__main__':
    unittest.main()
