"""Tests for document_message module."""
import unittest
from unittest.mock import MagicMock, patch

from message.document_message import DocumentMessageInput, DocumentMessageOutput


class TestDocumentMessageInput(unittest.TestCase):
    """Test cases for DocumentMessageInput class."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.mock_conversations = {}
        self.mock_conversations_service = MagicMock()
        self.mock_pipeline = MagicMock()
        self.mock_message_factory = MagicMock()
        self.mock_connector = MagicMock()
        self.mock_message_factory.connector = self.mock_connector
        self.mock_message_factory.data = {}
        self.mock_message_factory.mobile = "+1234567890"
        self.mock_cache_service = MagicMock()
        
        # Crear instancia bajo prueba
        self.document_message = DocumentMessageInput(
            conversations=self.mock_conversations,
            conversations_service=self.mock_conversations_service,
            pipeline=self.mock_pipeline,
            message_factory=self.mock_message_factory,
            cache_service=self.mock_cache_service
        )
        
        # Configurar el mock para get_document
        self.test_document_data = {
            "id": "test_document_id",
            "mime_type": "application/pdf"
        }
        self.mock_connector.get_document.return_value = self.test_document_data
        
        # Configurar el mock para query_media_url
        self.test_media_url = "https://example.com/document.pdf"
        self.mock_connector.query_media_url.return_value = self.test_media_url
        
        # Configurar el mock para download_media
        self.test_filename = "downloaded_document.pdf"
        self.mock_connector.download_media.return_value = self.test_filename
    
    @patch('message.document_message.logging')
    def test_handle_document_processing(self, mock_logging):
        """Test successful document processing."""
        # Llamar al método bajo prueba
        self.document_message.handle()
        
        # Verificar que se obtuvo el documento
        self.mock_connector.get_document.assert_called_once_with({})
        
        # Verificar que se consultó la URL del medio
        self.mock_connector.query_media_url.assert_called_once_with(
            self.test_document_data["id"]
        )
        
        # Verificar que se descargó el documento
        self.mock_connector.download_media.assert_called_once_with(
            self.test_media_url, self.test_document_data["mime_type"]
        )
        
        # Verificar el registro de log
        mock_logging.info.assert_called_once_with(
            f"{self.mock_message_factory.mobile} sent file {self.test_filename}"
        )


class TestDocumentMessageOutput(unittest.TestCase):
    """Test cases for DocumentMessageOutput class."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.mock_conversations = {}
        self.mock_conversations_service = MagicMock()
        self.mock_message_factory = MagicMock()
        self.mock_connector = MagicMock()
        self.mock_message_factory.connector = self.mock_connector
        self.mock_cache_service = MagicMock()
        
        # Crear instancia bajo prueba
        self.document_message = DocumentMessageOutput(
            conversations=self.mock_conversations,
            conversations_service=self.mock_conversations_service,
            message_factory=self.mock_message_factory,
            cache_service=self.mock_cache_service
        )
    
    def test_send_message_to_connector(self):
        """Test placeholder for send_message_to_connector."""
        # Este es un método placeholder que no hace nada en la implementación actual
        test_message = "Test message"
        test_mobile = "+1234567890"
        
        # Llamar al método bajo prueba
        self.document_message.send_message_to_connector(test_message, test_mobile)
        
        # Verificar que el método no hace nada (comportamiento actual)
        # En una implementación real, aquí irían las aserciones apropiadas
        pass


if __name__ == '__main__':
    unittest.main()
