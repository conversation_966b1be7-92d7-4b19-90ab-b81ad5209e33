import unittest
from dtos.client_data_dto import ClientDataDTO


# Clase de pruebas unitarias
class TestClientDataDTO(unittest.TestCase):

    def setUp(self):
        """Método que se ejecuta antes de cada prueba, para configurar datos comunes."""
        self.client_data = {
            "_id": "12345",
            "notify_number": "number",
            "before_instruction_prompt": "prompt before",
            "after_instruction_prompt": "prompt after",
            "notificate": True,
            "email": "<EMAIL>",
            "notificateToEmail": False,
            "rules": {},
            "assistant_id": "assistant_id",
            "finish_conversation_time": 30,
            "conversation_max_tokens_usage": 1024,
            "finish_conversation_vars": {}
        }

        self.dto = ClientDataDTO.build(self.client_data)

    def test_build_creates_instance(self):
        """Testea que el método build crea una instancia de ClientDataDTO correctamente."""
        self.assertIsInstance(self.dto, ClientDataDTO)

    def test_get_existing_key(self):
        """Testea que el método get devuelve el valor correcto para una clave existente."""
        self.assertEqual(self.dto.get("_id"), "12345")
        self.assertEqual(self.dto.get("email"), "<EMAIL>")

    def test_get_non_existing_key(self):
        """Testea que el método get devuelve el valor por defecto para una clave no existente."""
        self.assertIsNone(self.dto.get("non_existing_key"))
        self.assertEqual(self.dto.get("non_existing_key", "default_value"), "default_value")

    def test_initialization_with_kwargs(self):
        """Testea que la inicialización con kwargs establece correctamente los atributos."""
        self.assertEqual(self.dto.notify_number, "number")
        self.assertTrue(self.dto.notificate)


if __name__ == '__main__':
    unittest.main()
