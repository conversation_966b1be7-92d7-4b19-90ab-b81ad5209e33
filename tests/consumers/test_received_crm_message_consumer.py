import json
import unittest
from unittest.mock import MagicMock, patch, ANY

from consumers.received_crm_message_consumer import make_callback, start_received_crm_message_consumer
from enums.message_types import RoleMessageType


# Mock the dependencies
class MockMessageDTO:
    def __init__(self, **kwargs):
        self.__dict__.update(kwargs)

class TestReceivedCrmMessageConsumer(unittest.TestCase):
    def setUp(self):
        self.mock_conversations_service = MagicMock()
        self.callback = make_callback(self.mock_conversations_service)
        
        # Mock channel and method objects
        self.mock_channel = MagicMock()
        self.mock_method = MagicMock()
        self.mock_method.delivery_tag = 'test_delivery_tag'
        self.mock_properties = MagicMock()
        
    @patch('consumers.received_crm_message_consumer.MessageDTO')
    @patch('consumers.received_crm_message_consumer.ConnectorFactory')
    def test_make_callback_success(self, mock_connector_factory, mock_message_dto):
        # Setup test data
        test_message = {
            "payload": {
                "origin_id": "test_origin",
                "message": "Test message",
                "conversation_id": "test_conv_id"
            }
        }
        
        # Setup mocks
        mock_connector = MagicMock()
        mock_connector_factory.get_connector.return_value = mock_connector
        
        # Create a mock DTO instance
        mock_dto_instance = MagicMock()
        mock_dto_instance.origin_id = "test_origin"
        mock_dto_instance.message = "Test message"
        mock_dto_instance.conversation_id = "test_conv_id"
        mock_message_dto.return_value = mock_dto_instance
        
        # Call the callback
        self.callback(
            self.mock_channel,
            self.mock_method,
            self.mock_properties,
            json.dumps(test_message).encode('utf-8')
        )
        
        # Assertions
        mock_message_dto.assert_called_once_with(**test_message["payload"])
        mock_connector_factory.get_connector.assert_called_once_with("test_origin")
        mock_connector.send_message_to_connector.assert_called_once_with(message=mock_dto_instance)
        self.mock_conversations_service.update_chat_history_in_conversation.assert_called_once_with(
            conversation_id="test_conv_id",
            new_message={
                "role": ANY,  # We'll check the role in the next assertion
                "content": "Test message"
            }
        )
        # Check the role is using the RoleMessageType enum
        role = self.mock_conversations_service.update_chat_history_in_conversation.call_args[1]['new_message']['role']
        self.assertTrue(role in [e.to_string() for e in RoleMessageType])
    
    @patch('consumers.received_crm_message_consumer.json.loads')
    def test_make_callback_invalid_json(self, mock_json_loads):
        # Setup mock to raise JSON decode error
        mock_json_loads.side_effect = json.JSONDecodeError("Expecting value", "", 0)
        
        # Test that the JSON decode error is raised
        with self.assertRaises(json.JSONDecodeError):
            self.callback(
                self.mock_channel,
                self.mock_method,
                self.mock_properties,
                b'invalid json'
            )
        
        # Verify no other methods were called after the error
        self.mock_channel.basic_ack.assert_not_called()
        self.mock_channel.basic_nack.assert_not_called()
    
    @patch('consumers.received_crm_message_consumer.get_channel')
    def test_start_consumer(self, mock_get_channel):
        # Setup mock channel
        mock_channel = MagicMock()
        mock_connection = MagicMock()
        mock_get_channel.return_value = (mock_channel, mock_connection)
        
        # Call the start function
        start_received_crm_message_consumer(self.mock_conversations_service)
        
        # Assert channel setup
        mock_channel.queue_declare.assert_called_once_with(queue=ANY, durable=True)
        mock_channel.basic_consume.assert_called_once()
        
        # Get the callback function passed to basic_consume
        args, kwargs = mock_channel.basic_consume.call_args
        self.assertEqual(kwargs['auto_ack'], True)
        self.assertIsNotNone(kwargs['on_message_callback'])
        
        # Test that start_consuming is called
        mock_channel.start_consuming.assert_called_once()

if __name__ == '__main__':
    unittest.main()
