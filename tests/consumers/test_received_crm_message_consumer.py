import json
import unittest
from unittest.mock import MagicMock, patch
from consumers.received_crm_message_consumer import make_callback, start_received_crm_message_consumer

class TestReceivedCrmMessageConsumer(unittest.TestCase):
    def setUp(self):
        self.mock_conversations_service = MagicMock()
        self.callback = make_callback(self.mock_conversations_service)
        
        # Mock channel and method objects
        self.mock_channel = MagicMock()
        self.mock_method = MagicMock()
        self.mock_method.delivery_tag = 'test_delivery_tag'
        self.mock_properties = MagicMock()
        
    def test_make_callback_success(self):
        # Test data
        test_message = {
            "payload": {
                "origin_id": "test_origin",
                "message": "Test message",
                "conversation_id": "test_conv_id"
            }
        }
        
        # Mock the connector
        mock_connector = MagicMock()
        with patch('consumers.received_crm_message_consumer.ConnectorFactory.get_connector', 
                  return_value=mock_connector) as mock_get_connector:
            
            # Call the callback
            self.callback(
                self.mock_channel,
                self.mock_method,
                self.mock_properties,
                json.dumps(test_message).encode('utf-8')
            )
            
            # Assertions
            mock_get_connector.assert_called_once_with("test_origin")
            mock_connector.send_message_to_connector.assert_called_once()
            self.mock_channel.basic_ack.assert_called_once_with(delivery_tag='test_delivery_tag')
    
    def test_make_callback_invalid_json(self):
        with self.assertLogs(level='ERROR') as log:
            self.callback(
                self.mock_channel,
                self.mock_method,
                self.mock_properties,
                b'invalid json'
            )
            self.assertIn('Error processing CRM message', log.output[0])
            self.mock_channel.basic_nack.assert_called_once_with(
                delivery_tag='test_delivery_tag', requeue=False
            )
    
    @patch('consumers.received_crm_message_consumer.threading.Thread')
    @patch('consumers.received_crm_message_consumer.pika.BlockingConnection')
    @patch('consumers.received_crm_message_consumer.pika.ConnectionParameters')
    def test_start_consumer(self, mock_conn_params, mock_blocking_conn, mock_thread):
        # Call the start function
        start_received_crm_message_consumer(self.mock_conversations_service)
        
        # Assert thread was started
        mock_thread.assert_called_once()
        mock_thread.return_value.daemon = True
        mock_thread.return_value.start.assert_called_once()

if __name__ == '__main__':
    unittest.main()
