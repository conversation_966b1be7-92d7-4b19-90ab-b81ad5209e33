import json
import unittest
from unittest.mock import MagicMock, patch, ANY
from consumers.received_superchats_message_consumer import make_callback, start_received_superchats_message_consumer
from enums.connector_types import ConnectorType

# Mock the dependencies
class MockExecutor:
    def submit(self, *args, **kwargs):
        return args[0]  # Return the function to be executed

class TestReceivedSuperchatsMessageConsumer(unittest.TestCase):
    def setUp(self):
        self.mock_executor = MagicMock()
        self.mock_queue_manager = MagicMock()
        self.callback = make_callback(self.mock_executor, self.mock_queue_manager)
        
        # Mock channel and method objects
        self.mock_channel = MagicMock()
        self.mock_method = MagicMock()
        self.mock_method.delivery_tag = 'test_delivery_tag'
        self.mock_properties = MagicMock()
        
    @patch('consumers.received_superchats_message_consumer.json.loads')
    @patch('consumers.received_superchats_message_consumer.logging')
    def test_make_callback_success(self, mock_logging, mock_json_loads):
        # Test data
        test_message = {
            "message": "Test message",
            "from": "test_sender",
            "to": "test_recipient"
        }
        mock_json_loads.return_value = test_message
        
        # Setup mock executor to execute the function immediately
        def execute_immediately(fn):
            fn()
            return MagicMock()
        
        self.mock_executor.submit.side_effect = execute_immediately
        
        # Call the callback
        self.callback(
            self.mock_channel,
            self.mock_method,
            self.mock_properties,
            json.dumps(test_message).encode('utf-8')
        )
        
        # Assertions
        mock_json_loads.assert_called_once_with(ANY)
        mock_logging.info.assert_called_once_with(
            "Mensaje recibido de RabbitMQ: %s", test_message
        )
        self.mock_queue_manager.hook.assert_called_once_with(test_message, ConnectorType.WHATSAPP_VENOM)
        self.mock_channel.basic_ack.assert_called_once_with(delivery_tag='test_delivery_tag')
        self.mock_executor.submit.assert_called_once()
    
    @patch('consumers.received_superchats_message_consumer.json.loads')
    @patch('consumers.received_superchats_message_consumer.logging')
    def test_make_callback_invalid_json(self, mock_logging, mock_json_loads):
        # Setup mock to raise JSON decode error
        mock_json_loads.side_effect = json.JSONDecodeError("Expecting value", "", 0)
        
        # Call the callback
        self.callback(
            self.mock_channel,
            self.mock_method,
            self.mock_properties,
            b'invalid json'
        )
        
        # Assertions
        mock_logging.error.assert_called_once()
        error_msg = mock_logging.error.call_args[0][0]
        self.assertIn('Error procesando mensaje', error_msg)
        # Verify no ack was sent on error
        self.mock_channel.basic_ack.assert_not_called()
        # Verify queue_manager.hook was not called
        self.mock_queue_manager.hook.assert_not_called()
    
    @patch('consumers.received_superchats_message_consumer.get_channel')
    def test_start_consumer(self, mock_get_channel):
        # Setup mock channel
        mock_channel = MagicMock()
        mock_connection = MagicMock()
        mock_get_channel.return_value = (mock_channel, mock_connection)
        
        # Call the start function
        start_received_superchats_message_consumer(self.mock_executor, self.mock_queue_manager)
        
        # Assert channel setup
        mock_channel.queue_declare.assert_called_once_with(queue=ANY, durable=True)
        mock_channel.basic_qos.assert_called_once_with(prefetch_count=1)
        mock_channel.basic_consume.assert_called_once()
        
        # Get the callback function passed to basic_consume
        args, kwargs = mock_channel.basic_consume.call_args
        self.assertEqual(kwargs['queue'], ANY)
        self.assertEqual(kwargs['auto_ack'], False)
        self.assertIsNotNone(kwargs['on_message_callback'])
        
        # Test that start_consuming is called
        mock_channel.start_consuming.assert_called_once()

if __name__ == '__main__':
    unittest.main()
