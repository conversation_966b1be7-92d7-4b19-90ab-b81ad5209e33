import threading

def start_all_consumers(consumer_specs):
    """"
    Inicia todos los consumidores de mensajes de RabbitMQ.

    Args:
        consumer_specs (list): Lista de especificaciones de consumidores. Cada especificación es un diccionario con las siguientes claves:
            - 'start_func': Función para iniciar el consumidor.
            - 'args': Argumentos para la función de inicio del consumidor.
    """
    threads = []
    for func, services in consumer_specs:
        t = threading.Thread(target=func, args=services, daemon=True)
        t.start()
        threads.append(t)
    return threads