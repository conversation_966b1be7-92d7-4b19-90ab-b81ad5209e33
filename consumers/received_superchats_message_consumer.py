import json
import logging

from enums.connector_types import ConnectorType
from events.event_types import NOTIFY_RECEIVED_MESSAGE
from utils.rabbitmq_connection import get_channel


def make_callback(executor, queueManager):
    def callback(ch, method, properties, body):
        try:
            req_data = json.loads(body)
            logging.info("Mensaje recibido de RabbitMQ: %s", req_data)
            executor.submit(queueManager.hook, req_data, ConnectorType.WHATSAPP_VENOM)
            ch.basic_ack(delivery_tag=method.delivery_tag)
        except Exception as e:
            logging.error(f"Error procesando mensaje: {str(e)}")
            # No se hace ack, el mensaje se reencolará
    return callback


def start_received_superchats_message_consumer(executor, queueManager):
    channel, _ = get_channel()
    channel.queue_declare(queue=NOTIFY_RECEIVED_MESSAGE, durable=True)
    channel.basic_qos(prefetch_count=1)
    channel.basic_consume(
        queue=NOTIFY_RECEIVED_MESSAGE,
        on_message_callback=make_callback(executor, queueManager),
        auto_ack=False
    )
    channel.start_consuming()
