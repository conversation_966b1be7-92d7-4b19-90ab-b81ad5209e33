import json
import logging
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor

from enums.connector_types import ConnectorType
from events.event_types import NOTIFY_RECEIVED_MESSAGE, EXCHANGE_NAME
from utils.rabbitmq_connection import get_channel

SUPERCHATS_QUEUE_NAME = 'superchats_messages_queue'


def make_callback(executor, queue_manager):
    def callback(ch, method, properties, body):
        try:
            msg = json.loads(body)
            payload = msg["payload"]
            logging.info("Mensaje recibido de RabbitMQ: %s", payload)
            executor.submit(queue_manager.hook(payload, ConnectorType.WHATSAPP_VENOM))
            ch.basic_ack(delivery_tag=method.delivery_tag)
        except Exception as e:
            logging.error(f"Error procesando mensaje: {str(e)}")
            # No se hace ack, el mensaje se reencolará
    return callback


def start_received_superchats_message_consumer(queue_manager):
    executor = ThreadPoolExecutor(max_workers=4)
    channel, _ = get_channel()
    
    channel.exchange_declare(
        exchange=EXCHANGE_NAME,
        exchange_type='topic',
        durable=True
    )
    
    channel.queue_declare(queue=SUPERCHATS_QUEUE_NAME, durable=True)
    
    channel.queue_bind(
        exchange=EXCHANGE_NAME,
        queue=SUPERCHATS_QUEUE_NAME,
        routing_key=NOTIFY_RECEIVED_MESSAGE
    )
    
    channel.basic_qos(prefetch_count=1)
    channel.basic_consume(
        queue=SUPERCHATS_QUEUE_NAME,
        on_message_callback=make_callback(executor, queue_manager),
        auto_ack=False
    )
    logging.info(f"Escuchando mensajes en la cola {SUPERCHATS_QUEUE_NAME} con routing_key {NOTIFY_RECEIVED_MESSAGE}")
    channel.start_consuming()
