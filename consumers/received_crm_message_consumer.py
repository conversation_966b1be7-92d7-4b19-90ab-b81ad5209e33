import json
import logging

from connectors.connector_factory import ConnectorFactory
from dtos.crm_message_dto import MessageDTO
from enums.message_types import RoleMessageType
from events.event_types import CRM_SEND_MESSAGE, EXCHANGE_NAME
from utils.rabbitmq_connection import get_channel

PYTHON_MESSAGE_RECEIVED_QUEUE_NAME = 'python_messages_received'


def make_callback(conversations_service):
    def callback(ch, method, properties, body):
        msg = json.loads(body)
        payload = msg["payload"]
        crm_new_message_dto = MessageDTO(**payload)
        connector = ConnectorFactory.get_connector(crm_new_message_dto.origin_id)
        connector.send_message_to_connector(
            message=crm_new_message_dto
        )
        new_message = {
            "role": RoleMessageType.ASSISTANT.to_string(),
            "content": crm_new_message_dto.message
        }
        conversations_service.update_chat_history_in_conversation(
            conversation_id=crm_new_message_dto.conversation_id,
            new_message=new_message
        )
    return callback


def start_received_crm_message_consumer(conversations_service):
    channel, _ = get_channel()
    channel.exchange_declare(
        exchange=EXCHANGE_NAME,
        exchange_type='topic',
        durable=True
    )
    channel.queue_declare(queue=PYTHON_MESSAGE_RECEIVED_QUEUE_NAME, durable=True)
    channel.queue_bind(
        exchange=EXCHANGE_NAME,
        queue=PYTHON_MESSAGE_RECEIVED_QUEUE_NAME,
        routing_key=CRM_SEND_MESSAGE
    )
    channel.basic_consume(
        queue=PYTHON_MESSAGE_RECEIVED_QUEUE_NAME,
        on_message_callback=make_callback(conversations_service=conversations_service),
        auto_ack=True
    )
    logging.info(f"Escuchando mensajes en la cola {PYTHON_MESSAGE_RECEIVED_QUEUE_NAME} con routing_key {CRM_SEND_MESSAGE}")
    channel.start_consuming()
