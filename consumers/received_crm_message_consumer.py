import json

from connectors.connector_factory import ConnectorFactory
from dtos.crm_message_dto import MessageDTO
from enums.message_types import RoleMessageType
from events.event_types import CRM_SEND_MESSAGE
from utils.rabbitmq_connection import get_channel


def make_callback(conversations_service):
    def callback(ch, method, properties, body):
        msg = json.loads(body)
        payload = msg["payload"]
        crm_new_message_dto = MessageDTO(**payload)
        connector = ConnectorFactory.get_connector(crm_new_message_dto.origin_id)
        connector.send_message_to_connector(
            message=crm_new_message_dto
        )
        new_message = {
            "role": RoleMessageType.ASSISTANT.to_string(),
            "content": crm_new_message_dto.message
        }
        conversations_service.update_chat_history_in_conversation(
            conversation_id=crm_new_message_dto.conversation_id,
            new_message=new_message
        )
    return callback


def start_received_crm_message_consumer(conversations_service):
    channel, _ = get_channel()
    channel.queue_declare(queue=CRM_SEND_MESSAGE, durable=True)
    channel.basic_consume(
        queue=CRM_SEND_MESSAGE,
        on_message_callback=make_callback(conversations_service=conversations_service),
        auto_ack=True
    )
    channel.start_consuming()
