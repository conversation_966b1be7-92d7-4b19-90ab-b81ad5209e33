"""
Lead Manager - Handles lead generation and management for conversations.
Manages the creation and updating of leads in a thread-safe manner.
"""
import json
import logging
from typing import Dict, Any, Optional

from ai.assistant import AssistantManager
from ai.chatgpt import get_chatgpt_response
from db.models import Conversation, Lead
from enums.message_types import RoleMessageType
from events.event_types import LEAD_CREATED
from lead.integrations.integration_manager import IntegrationManager
from message.factory.message_factory import MessageFactory
from metrics import GENERATE_LEAD_ERROR, GENERATE_LEAD_COUNT, UPDATE_LEAD_COUNT, GENERATE_LEAD_LATENCY, UPDATE_LEAD_ERROR
from producers.event_producer import send_event
from prompts import COMPLETE_TAGS, COMPLETE_COLUMNS
from services.cache import CacheService
from services.conversation import ConversationService
from utils.session_timer import SessionTimer
from utils.utils import get_user_to_assign

MILKAUT_CLIENT_ID = "65dcaba815b511cc027ac950"

class LeadManager:
    """
    Manages the lifecycle of leads generated from conversations.
    Handles creation, updating, and integration with external systems.
    """
    def __init__(
        self, 
        session_timer: SessionTimer, 
        conversations_service: ConversationService,
        connector, 
        message_factory: MessageFactory, 
        cache_service: CacheService,
        cache_data: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize the LeadManager with required services and data.
        
        Args:
            session_timer: For managing session timeouts
            conversations_service: Service for conversation operations
            connector: Connector for sending messages
            message_factory: Factory for creating message objects
            cache_service: Service for caching conversation data
            cache_data: Optional cached data for the conversation
        """
        self.hash = message_factory.hash
        self.connector = connector
        self.mobile = message_factory.mobile
        self.name = message_factory.name
        self.session_timer = session_timer
        self.conversations_service = conversations_service
        self.client_id = message_factory.client_id
        self.wa_id = message_factory.mobile
        self.assistant_id = message_factory.assistant_id
        self.before_instruction_prompt = message_factory.before_instruction_prompt
        self.after_instruction_prompt = message_factory.after_instruction_prompt
        self.finish_conversation_vars = message_factory.finish_conversation_vars
        self.client_columns = message_factory.client_columns
        self.client_tags = message_factory.client_tags
        self.integrations = message_factory.integrations
        self.cache_service = cache_service
        self.cache_data = cache_data or {}
        self.logger = logging.getLogger(__name__)

    def generate_or_update_lead(self, update_conversation: bool):
        """
        Generate a new lead or update an existing one.
        
        Args:
            update_conversation: If True, update existing lead; otherwise create new
        """
        with GENERATE_LEAD_LATENCY.time():
            if update_conversation:
                self.update_lead()
                UPDATE_LEAD_COUNT.inc()
            else:
                self.generate_lead()
                GENERATE_LEAD_COUNT.inc()

    def delete_conversation_local_and_cache(self) -> bool:
        """
        Delete local conversation data and cache.
        
        Returns:
            bool: True if deletion was successful, False otherwise
        """
        try:
            deleted = self.session_timer.delete_session(self.hash)
            self.cache_service.delete_cache_value(key=self.hash)
            
            if deleted:
                self.logger.info(f"Successfully deleted session for mobile {self.hash}")
            else:
                self.logger.info(f"No active session found for mobile {self.hash}")
            return deleted
            
        except Exception as e:
            self.logger.error(
                f"Error deleting conversation for mobile {self.hash}: {str(e)}",
                exc_info=True
            )
            return False

    def generate_lead(self) -> None:
        """
        Generate a new lead from the current conversation.
        
        This method extracts conversation data and creates a new lead in the system.
        """
        try:
            thread_id = self.cache_data.get('thread_id')
            if not thread_id:
                self.logger.error("Could not get thread_id for lead generation")
                GENERATE_LEAD_ERROR.inc()
                return
                
            assistant = AssistantManager.get_assistant_by_id(assistant_id=self.assistant_id)
            all_messages = assistant.get_messages_in_thread_formatted(thread_id=thread_id)
            
            conversation = self.get_final_conversation(all_messages=all_messages)
            
            if self.client_id == MILKAUT_CLIENT_ID:
                optin = conversation.additional_fields.get(
                    'respondio_afirmativamente_para_recibir_comunicaciones', 
                    None
                )
                if optin:
                    self.run_integrations(conversation=conversation)
                conversation.lead.phone = None
            else:
                self.run_integrations(conversation=conversation)
            
            conversation_id = self.save_conversation(conversation, thread_id)
            
            if conversation.status != "nuevo" and not conversation.active:
                self.delete_conversation_local_and_cache()
            
            send_event(
                event_type=LEAD_CREATED,
                payload={
                    "payload": {
                        "assign": conversation.assign,
                        "leadId": conversation_id,
                    }
                }
            )
            
            self.logger.info(f"Lead generated successfully for {self.mobile}")
            
        except Exception as e:
            self.logger.error(
                f"Error generating lead for {self.mobile}: {str(e)}",
                exc_info=True
            )
            GENERATE_LEAD_ERROR.inc()
            raise

    def update_lead(self) -> None:
        try:
            thread_id = self.cache_data.get('thread_id')
            token_usage = self.cache_data.get('token_usage', 0)
            conversation_id = self.cache_data.get('conversation_id')
            
            if not all([thread_id, conversation_id]):
                self.logger.error(
                    f"There is not enough information to update the lead. "
                    f"Thread ID: {thread_id}, Conversation ID: {conversation_id}"
                )
                return
            
            assistant = AssistantManager.get_assistant_by_id(self.assistant_id)
            all_messages = assistant.get_messages_in_thread_formatted(thread_id=thread_id)
            
            if not all_messages:
                self.logger.warning(f"No messages found for the thread {thread_id}")
                return
                
            # Eliminar el mensaje de bienvenida
            all_messages.pop(0)
            
            self.conversations_service.update_conversations_with_contact(
                conversation_id=conversation_id,
                token_usage=token_usage,
                all_messages=all_messages
            )
            
            self.logger.info(
                f"Lead successfully updated for {self.mobile}. "
                f"Messages: {len(all_messages)}, Tokens used: {token_usage}"
            )
            
        except Exception as e:
            self.logger.error(
                f"Error updating lead for {self.mobile}: {str(e)}",
                exc_info=True
            )
            UPDATE_LEAD_ERROR.inc()
            raise

    def get_final_conversation(self, all_messages: list) -> Optional[Conversation]:
        if not all_messages:
            self.logger.warning("There are no messages to build the conversation")
            return None
            
        try:
            all_messages.pop(0)
            
            token_usage = self.cache_data.get('token_usage', 0)
            conversation = self.build_json_conversation(all_messages, token_usage)
            return conversation
        except KeyError as ke:
            self.logger.error(
                f"Required information is missing from cache_data for {self.mobile}: {str(ke)}",
                exc_info=True
            )
        except json.JSONDecodeError as je:
            self.logger.error(
                f"Error decoding API response for {self.mobile}: {str(je)}",
                exc_info=True
            )
        except Exception as e:
            self.logger.error(
                f"Unexpected error while building conversation for {self.mobile}: {str(e)}",
                exc_info=True
            )

    def save_conversation(self, conversation: Conversation, thread_id: str) -> Optional[str]:
        if not conversation:
            self.logger.error("Cannot save a null conversation")
            return None
            
        try:
            conversation_id = self.conversations_service.insert_conversation(conversation)
            if not conversation_id:
                raise ValueError("Failed to insert conversation into database")
                
            conversation_id_str = str(conversation_id)
            
            self.cache_service.set_cache_value(
                key=self.hash,
                thread_id=thread_id,
                conversation_id=conversation_id_str,
                token_usage=self.cache_data.get('token_usage', 0),
                update_conversation=False
            )
            
            self.logger.info(f"Conversation saved with ID {conversation_id} for hash {self.hash}")
            return conversation_id_str
            
        except Exception as e:
            self.logger.error(
                f"Error saving conversation for {self.mobile}: {str(e)}",
                exc_info=True
            )
            return None

    def build_json_conversation(self, chat_history: list, token_usage: int) -> Optional[Conversation]:
        if not chat_history:
            self.logger.warning("No chat history to build the conversation")
            return None
            
        try:
            lead = Lead(
                fullname=self.name, 
                email=None, 
                phone=self.mobile, 
                hash=self.hash
            )
            
            assign = get_user_to_assign(self.client_id)
            
            tags = self.get_tags(conversation=chat_history.copy())
            columns_ids = self.get_columns(conversation=chat_history.copy())
            additional_fields, status, summary = self.get_conversations_vars(
                conversation=chat_history.copy()
            )
            
            is_new = status == "nuevo"
            
            conversation = Conversation(
                chathistory=chat_history,
                active=is_new,
                status=status,
                lead=lead,
                clientid=self.client_id,
                summary=summary,
                origin=self.connector.origin,
                origin_id=self.connector.origin_id,
                tokenusage=token_usage,
                beforeinstructionprompt=self.before_instruction_prompt,
                afterinstructionprompt=self.after_instruction_prompt,
                additional_fields=additional_fields or {},
                assign=assign,
                columns_ids=columns_ids or [],
                botstop=is_new,
                tags=tags or []
            )
            
            self.logger.debug(f"Conversation built for {self.mobile} with status '{status}'")
            return conversation
            
        except Exception as e:
            self.logger.error(
                f"Error building conversation for {self.mobile}: {str(e)}",
                exc_info=True
            )
            return None

    def run_integrations(self, conversation: Conversation) -> None:
        """
        Execute configured integrations for the lead.
        
        Args:
            conversation: The conversation object containing lead data
        """
        try:
            if not self.integrations:
                self.logger.debug("No integrations configured for this lead")
                return
            
            self.logger.info(f"Executing {len(self.integrations)} integrations for {self.mobile}")
            integration_manager = IntegrationManager()
            integration_manager.process_integrations(
                integrations=self.integrations, 
                client_data=conversation
            )
            
            self.logger.info(f"Completed integrations for {self.mobile}")
            
        except Exception as e:
            self.logger.error(
                f"Error running integrations for {self.mobile}: {str(e)}",
                exc_info=True
            )

    def get_tags(self, conversation: list) -> list:
        if not self.client_tags:
            self.logger.debug("No tags configured for the client")
            return []
            
        try:
            get_tags_message = COMPLETE_TAGS.format(tags=self.client_tags)
            conversation.append({
                "role": RoleMessageType.SYSTEM.to_string(), 
                "content": get_tags_message
            })
            
            response = get_chatgpt_response(
                questions=conversation,
                json_response=True
            )
            
            response_data = json.loads(response['message'])
            tags = response_data.get('tags', [])
            
            self.logger.debug(f"Tags obtained for {self.mobile}: {tags}")
            return tags
            
        except json.JSONDecodeError as je:
            self.logger.error(
                f"Error decoding tag response for {self.mobile}: {str(je)}"
            )
            return []
        except Exception as e:
            self.logger.error(
                f"Error getting tags for {self.mobile}: {str(e)}",
                exc_info=True
            )
            return []

    def get_columns(self, conversation: list) -> list:
        if not self.client_columns:
            self.logger.debug("There are no columns configured for the client")
            return []
            
        try:
            get_columns_message = COMPLETE_COLUMNS.format(columns=self.client_columns)
            conversation.append({
                "role": RoleMessageType.SYSTEM.to_string(), 
                "content": get_columns_message
            })
            
            response = get_chatgpt_response(
                questions=conversation,
                json_response=True
            )
            response_data = json.loads(response['message'])
            columns_ids = response_data.get('columns_ids', [])
            
            self.logger.debug(f"Columns obtained for {self.mobile}: {columns_ids}")
            return columns_ids
            
        except json.JSONDecodeError as je:
            self.logger.error(
                f"Error decoding column response for {self.mobile}: {str(je)}"
            )
            return []
        except Exception as e:
            self.logger.error(
                f"Error getting columns for {self.mobile}: {str(e)}",
                exc_info=True
            )
            return []

    def get_conversations_vars(self, conversation: list) -> tuple[dict, str, str]:
        try:
            conversation.append({
                "role": RoleMessageType.SYSTEM.to_string(), 
                "content": self.finish_conversation_vars
            })
            
            response = get_chatgpt_response(
                questions=conversation,
                json_response=True
            )
            
            response_data = json.loads(response['message'])
            additional_fields = response_data.get('variables', {})
            status = response_data.get('status', 'nuevo')
            summary = response_data.get('resumen', '')
            
            self.logger.debug(
                f"Variables obtained for {self.mobile}: "
                f"status={status}, fields={len(additional_fields)}"
            )
            
            return additional_fields, status, summary
            
        except json.JSONDecodeError as je:
            self.logger.error(
                f"Error decoding response variables for {self.mobile}: {str(je)}"
            )
            return {}, "", ""
        except Exception as e:
            self.logger.error(
                f"Error getting variables for {self.mobile}: {str(e)}",
                exc_info=True
            )
            return {}, "", ""
