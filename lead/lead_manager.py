import json
import logging

from ai.assistant import AssistantManager
from ai.chatgpt import get_chatgpt_response
from db.models import Conversation, Lead
from enums.message_types import RoleMessageType
from events.event_types import LEAD_CREATED
from lead.integrations.integration_manager import IntegrationManager
from message.factory.message_factory import MessageFactory
from metrics import GENERATE_LEAD_ERROR, GENERATE_LEAD_COUNT, UPDATE_LEAD_COUNT, GENERATE_LEAD_LATENCY
from producers.event_producer import send_event
from prompts import COMPLETE_VARS
from rules.rules import *
from utils.utils import send_email, get_user_to_assign

MILKAUT_CLIENT_ID = "65dcaba815b511cc027ac950"

class LeadManager:
    def __init__(self, conversations, conversations_service, connector, message_factory: MessageFactory, cache_service):
        self.hash = message_factory.hash
        self.connector = connector
        self.mobile = message_factory.mobile
        self.name = message_factory.name
        self.conversations = conversations
        self.conversations_service = conversations_service
        self.client_id = message_factory.client_id
        self.notify_number = message_factory.notify_number
        self.notificate = message_factory.notificate
        self.client_email = message_factory.client_email
        self.notificate_to_email = message_factory.notificate_to_email
        self.wa_id = message_factory.mobile
        self.rules = message_factory.rules
        self.assistant_id = message_factory.assistant_id
        self.before_instruction_prompt = message_factory.before_instruction_prompt
        self.after_instruction_prompt = message_factory.after_instruction_prompt
        self.finish_conversation_vars = message_factory.finish_conversation_vars
        self.integrations = message_factory.integrations
        self.cache_service = cache_service

    def generate_or_update_lead(self, update_conversation: bool):
        with GENERATE_LEAD_LATENCY.time():
            if update_conversation:
                self.update_lead()
                UPDATE_LEAD_COUNT.inc()
            else:
                self.generate_lead()
                GENERATE_LEAD_COUNT.inc()

    def generate_lead(self):
        thread_id = self.conversations[self.hash]['thread_id']
        all_messages = AssistantManager.get_assistant_by_id(self.assistant_id).get_messages_in_thread_formatted(
            thread_id=thread_id)

        result = apply_regex(self.rules, all_messages)
        send_lead = validate_rules(self.rules, result)

        conversation = self.get_final_conversation(all_messages=all_messages,
                                                   finish_conversation_json=self.finish_conversation_vars)

        if not send_lead:
            conversation.status = "sincontacto"
            conversation.active = False
            self.save_conversation(conversation, thread_id)
            return

        conversation.lead.update_from_json(conversation.lead, result)

        if self.client_id == MILKAUT_CLIENT_ID:
            optin = conversation.additional_fields.get('respondio_afirmativamente_para_recibir_comunicaciones', None)
            if optin:
                self.run_integrations(conversation=conversation)
            conversation.lead.phone = None
        else:
            self.run_integrations(conversation=conversation)

        self.save_conversation(conversation, thread_id)
        data = {
            "assign": conversation.assign,
            "leadId": self.conversation_id,
        }
        send_event(event_type=LEAD_CREATED, payload={
            "payload": data
        })
        if self.notificate:
            self.notificate_lead(self.mobile, conversation)
        if self.notificate_to_email:
            send_email(self.client_email, conversation)
        return

    def update_lead(self):
        thread_id = self.conversations[self.hash]['thread_id']
        all_messages = AssistantManager.get_assistant_by_id(self.assistant_id).get_messages_in_thread_formatted(thread_id=thread_id)

        result = apply_regex(self.rules, all_messages)
        send_lead = validate_rules(self.rules, result)

        # Elimino el primer mensaje de la bienvenida
        all_messages.pop(0)

        token_usage = self.conversations[self.hash]['token_usage']

        if not send_lead:
            self.conversations_service.update_conversations_without_contact(
                conversation_id=self.conversations[self.hash]['conversation_id'],
                token_usage=token_usage,
                all_messages=all_messages
            )
            return

        self.conversations_service.update_conversations_with_contact(
            conversation_id=self.conversations[self.hash]['conversation_id'],
            token_usage=token_usage,
            all_messages=all_messages
        )

    def get_final_conversation(self, all_messages, finish_conversation_json):
        complete_vars_message = COMPLETE_VARS.format(finish_conversation_json)
        messages_secondary = all_messages.copy()
        messages_secondary.append({"role": RoleMessageType.SYSTEM.to_string(), "content": complete_vars_message})

        response = get_chatgpt_response(questions=messages_secondary, json_response=True)
        all_messages.pop(0)

        try:
            conversation_vars_json_string = response["message"]
            conversation_vars = json.loads(conversation_vars_json_string)
            token_usage = self.conversations[self.hash]['token_usage']
            conversation = self.build_json_conversation(conversation_vars, all_messages, token_usage)
            return conversation
        except Exception as e:
            GENERATE_LEAD_ERROR.inc()
            logging.error(f"Error in build conversation for {self.hash}: {str(e)}\n")

    def save_conversation(self, conversation: Conversation, thread_id: str):
        conversation_id = self.conversations_service.insert_conversation(conversation)
        self.conversation_id = str(conversation_id)
        self.cache_service.set_cache_value(
            key=self.hash,
            thread_id=thread_id,
            conversation_id=str(conversation_id)
        )
        logging.info(f"Lead generate with id {conversation_id} for hash {self.hash}")

    def build_json_conversation(self, conversation_vars, chat_history, token_usage):
        lead = Lead(fullname=self.name, email=None, phone=self.mobile, hash=self.hash)
        additional_fields = {key: value for key, value in conversation_vars.items()}
        assign = get_user_to_assign(self.client_id)
        tags = self.extract_tags(additional_fields=additional_fields)

        status = additional_fields.get('status', 'nuevo')

        conversation = Conversation(
            chathistory=chat_history,
            active=True,
            status=status,
            lead=lead,
            clientid=self.client_id,
            summary=additional_fields.get('resumen', None),
            origin=self.connector.origin,
            origin_id=self.connector.origin_id,
            tokenusage=token_usage,
            beforeinstructionprompt=self.before_instruction_prompt,
            afterinstructionprompt=self.after_instruction_prompt,
            additional_fields=additional_fields,
            assign=assign,
            botstop=True,
            tags=tags
        )

        return conversation

    def run_integrations(self, conversation: Conversation):
        integration_manager = IntegrationManager()
        integration_manager.process_integrations(integrations=self.integrations, client_data=conversation)

    def extract_tags(self, additional_fields):
        tags_dict = additional_fields.pop("tags", {})
        tags = [
            key for key, value in tags_dict.items()
            if isinstance(value, str) and value.lower() == "true"
        ]
        return tags

    def notificate_lead(self, mobile, conversation: Conversation):
        contacts = [{
            "name": {
                "formatted_name": conversation.lead.fullname,
                "first_name": conversation.lead.fullname,
            },
            "phones": [{
                "phone": mobile,
                "type": "MAIN",
                "wa_id": self.wa_id
            }]
        }]
        if conversation.summary is None:
            conversation.summary = "El cliente no dejo informacion"
        leadNotificationMessage = f"""
    ¡Hola!
    *Se acaba de generar un Lead:*
    *Resumen:* {conversation.summary}

    ¡Saludos!
    """
        self.connector.send_message(leadNotificationMessage, self.notify_number)
        self.connector.send_contacts(contacts, self.notify_number)
