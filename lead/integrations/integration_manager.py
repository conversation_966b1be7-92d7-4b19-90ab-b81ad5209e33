import logging
from lead.integrations.mailchimp.mailchimp_integration import MailchimpIntegration
from lead.integrations.mailchimp.mailchimp_dto import MailchimpDTO


class IntegrationManager:
    def __init__(self):
        self.integration_classes = {
            "mailchimp": MailchimpIntegration
        }

        self.dto_classes = {
            "mailchimp": MailchimpDTO,
        }

    def _build_dto(self, integration_name, client_data):
        """Construye el DTO adecuado para cada integración"""
        dto_class = self.dto_classes.get(integration_name)
        if dto_class:
            return dto_class(client_data)
        else:
            logging.warning(f"No hay DTO definido para la integración: {integration_name}")
            return None

    def process_integrations(self, integrations, client_data):
        for integration_name in integrations:
            integration_class = self.integration_classes.get(integration_name)
            if integration_class:
                integration_instance = integration_class()
                integration_dto = self._build_dto(integration_name, client_data)

                if integration_dto:
                    integration_instance.execute(integration_dto)
            else:
                logging.warning(f"No hay integración definida para: {integration_name}")
