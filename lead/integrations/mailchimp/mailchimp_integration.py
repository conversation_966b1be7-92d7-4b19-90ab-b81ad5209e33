import os
import logging
import mailchimp_marketing as MailchimpMarketing
from mailchimp_marketing.api_client import ApiClientError


class MailchimpIntegration:
    def __init__(self):
        self.api_key = os.environ.get('MAILCHIMP_APIKEY')
        self.server_prefix = os.environ.get('MAILCHIMP_SERVER')
        self.client = self._initialize_mailchimp_client()

    def _initialize_mailchimp_client(self):
        _client = MailchimpMarketing.Client()
        _client.set_config({
            "api_key": self.api_key,
            "server": self.server_prefix
        })
        return _client

    def execute(self, client_data):
        list_id = os.environ.get('MAILCHIMP_LISTID')
        email = getattr(client_data, "email", None)
        if not email:
            logging.error("[MailchimpIntegration] No email provided, cannot add list member")
            return

        suscriptor = {
            "email_address": email,
            "status": "subscribed",  # Estado: puede ser "subscribed", "unsubscribed", "pending", "cleaned"
            "merge_fields": {
            "NOMBRE": getattr(client_data, "nombre", ""),
            "APELLIDO": getattr(client_data, "apellido", ""),
            "CELULAR": getattr(client_data, "celular", ""),
            "CONVER": getattr(client_data, "conversacion", ""),
            "PAIS": "Argentina",
            },
            "tags": ["Suscriptores Chatbot"]
        }
 
        try:
            self.client.lists.add_list_member(list_id, suscriptor)
            logging.info("[MailchimpIntegration] Add List Member success")
        except ApiClientError as error:
            logging.error(f"[MailchimpIntegration] Add List Member error: {error.text}")
