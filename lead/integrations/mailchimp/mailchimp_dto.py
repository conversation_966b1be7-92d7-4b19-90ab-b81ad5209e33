from lead.integrations.integration_dto import IntegrationDTO
from db.models import Conversation


class MailchimpDTO(IntegrationDTO):
    def __init__(self, conversation: Conversation):
        self.email = getattr(conversation.lead, "email", None)
        self.nombre = conversation.additional_fields.get('nombre', "")
        self.apellido = conversation.additional_fields.get('apellido', "")
        self.celular = getattr(conversation.lead, "phone", "")
        self.conversacion = self.format_chathistory(getattr(conversation, "chathistory", []))

    def format_chathistory(self, chathistory):
        formatted_messages = [
            f"{'Asistente' if message.role == 'assistant' else 'Cliente'}: {message.content} - " 
            for message in chathistory
        ]
        return "\n".join(formatted_messages)