# lead_crm_library/hubspot/leads.py

from .auth import <PERSON><PERSON><PERSON><PERSON>uth
from .exceptions import HubspotError
from .utils import normalize_lead_data
from hubspot.crm.contacts import SimplePublicObjectInput
from hubspot.crm.contacts.exceptions import ApiException


class HubspotLeads:
    def __init__(self, api_key):
        self.api_key = api_key
        self.auth = HubspotAuth(api_key=self.api_key)

    def add_lead(self, lead_data):
        try:
            # Autenticarse con la API Key de HubSpot
            hubspot_client = self.auth.authenticate()

            simple_public_object_input = SimplePublicObjectInput(
                properties=lead_data['properties']
            )

            # Normalizar los datos del lead antes de agregarlos a HubSpot
            # normalized_data = normalize_lead_data(lead_data)

            # Código para agregar un lead a HubSpot usando la API Key y los datos normalizados
            hubspot_client.crm.contacts.basic_api.create(
                simple_public_object_input=simple_public_object_input
            )

        except ApiException as e:
            raise HubspotError("Exception when creating contact: %s\n" % e)
