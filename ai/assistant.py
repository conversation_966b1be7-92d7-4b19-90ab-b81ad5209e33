from datetime import datetime
from zoneinfo import ZoneInfo
from functools import lru_cache

from ai.openai_client import get_client_instance
from enums.message_types import RoleMessageType
from events.event_handler import EventHandler


@lru_cache(maxsize=128)
def get_zoneinfo(timezone_name):
    return ZoneInfo(timezone_name)


class Assistant:
    def __init__(self, assistant_id):
        self.client = get_client_instance()
        self.assistant_id = assistant_id

    # Get Timezone Name: https://utctime.info/timezone/
    def create_user_message(self, thread_id, message_content, role, timezone_name="America/Argentina/Buenos_Aires"):
        try:
            timezone = get_zoneinfo(timezone_name)
        except Exception:
            timezone = get_zoneinfo('UTC')

        now = datetime.now(timezone)

        timestamp = now.strftime("%H:%M:%S %d/%m/%Y")
        utc_offset = now.strftime('%z')
        utc_offset_formatted = f"UTC{utc_offset[:3]}:{utc_offset[3:]}"

        message_with_timestamp = f"{message_content} | {timestamp} {utc_offset_formatted}"
        return self.client.beta.threads.messages.create(
            thread_id=thread_id,
            role=role.to_string(),
            content=message_with_timestamp
        )

    def create_thread(self):
        return self.client.beta.threads.create()

    def run_assistant(self, thread_id):
        return self.client.beta.threads.runs.create(
            thread_id=thread_id,
            assistant_id=self.assistant_id
        )

    def run_assistant_with_stream(self, thread_id, message_factory, cache_service, pipeline):
        with self.client.beta.threads.runs.stream(
                thread_id=thread_id,
                assistant_id=self.assistant_id,
                event_handler=EventHandler(
                    thread_id=thread_id,
                    assistant_id=self.assistant_id,
                    message_factory=message_factory,
                    cache_service=cache_service,
                    pipeline=pipeline
                ),
        ) as stream:
            stream.until_done()

    def get_run(self, thread_id, run_id):
        return self.client.beta.threads.runs.retrieve(
            thread_id=thread_id,
            run_id=run_id
        )

    def get_messages_in_thread(self, thread_id):
        return self.client.beta.threads.messages.list(
            thread_id=thread_id,
            order="asc"
        )

    def get_messages_in_thread_formatted(self, thread_id):
        format_messages = []
        all_messages = self.get_messages_in_thread(thread_id=thread_id)
        for msg in all_messages.data:
            role = msg.role
            content = msg.content[0].text.value
            format_messages.append({"role": role, "content": content})
        return format_messages

    def get_user_message_response(self, thread_id):
        messages = self.get_messages_in_thread(thread_id=thread_id)
        last_id = messages.last_id
        for msg in messages.data:
            if msg.id == last_id:
                role = msg.role
                content = msg.content[0].text.value
                if role == RoleMessageType.ASSISTANT.to_string():
                    return content


class AssistantManager:
    _instances = {}

    @classmethod
    def get_assistant_by_id(cls, assistant_id) -> Assistant:
        if assistant_id not in cls._instances:
            cls._instances[assistant_id] = Assistant(assistant_id)
        return cls._instances[assistant_id]
