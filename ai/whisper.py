from pydub import AudioSegment
from ai.openai_client import get_client_instance

TIME_LIMIT = 30


def speech_to_text(audio_filename):
    client = get_client_instance()
    # Convertir el archivo de audio a formato WAV
    audio_converted = convert_audio_format(audio_filename)
    # Cargar el archivo de audio usando PyDub
    audio_file = AudioSegment.from_file(audio_converted)

    # Obtener la duración del archivo de audio en segundos
    duration_sec = len(audio_file) / 1000

    # Comprobar que la duración del archivo de audio sea menor al TIME_LIMIT
    if duration_sec <= TIME_LIMIT:
        # Transcribir el audio usando OpenAI API
        with open(audio_converted, "rb") as audio_file:
            transcript_es = client.audio.transcriptions.create(file=audio_file,
                                                    model="whisper-1",
                                                    response_format="text",
                                                    language="es")
        return transcript_es
    else:
        error_msg = f"Disculpe pero no puedo escuchar audios de mas de {TIME_LIMIT} segundos"
        raise AudioTooLongException(error_msg)


def convert_audio_format(audio_filename):
    # Cargar el archivo de audio usando PyDub
    audio_file = AudioSegment.from_file(audio_filename, format="ogg")

    # Crear un nuevo nombre de archivo para el archivo de audio convertido
    new_filename = audio_filename.split(".")[0] + ".wav"

    # Convertir el archivo de audio a formato WAV
    audio_file.export(new_filename, format="wav")

    return new_filename


class AudioTooLongException(Exception):
    pass
