import logging
from ai.openai_client import get_client_instance
import os

GPT_MODEL = os.environ.get('GPT_MODEL', 'gpt-4.1-mini')

def get_chatgpt_response(questions, json_response: bool):
    client = get_client_instance()
    try:
        if json_response:
            response = client.chat.completions.create(
                model=GPT_MODEL,
                messages=questions,
                temperature=0.3,
                max_tokens=300,
                response_format={"type": "json_object"}
            )
        else:
            response = client.chat.completions.create(
                model=GPT_MODEL,
                messages=questions,
                temperature=0.3,
                max_tokens=300,
            )
        response_message = response.choices[0].message
        response_bill = response.usage
        return {"message": response_message.content, "bill": response_bill.total_tokens}
    except Exception as e:
        logging.error(f"Unable to generate ChatCompletion response error: {e}")
        return e
