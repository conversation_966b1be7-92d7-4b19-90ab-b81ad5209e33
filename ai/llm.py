from gtts import gTTS
import tempfile
import os
import pyttsx4


def text_to_speech(text):
    # Crear objeto gTTS
    tts = gTTS(text, lang="es", slow=False)
    audio_file = tempfile.NamedTemporaryFile(suffix=".mp3", delete=False)
    tts.save(audio_file.name)
    return audio_file


def delete_audio_file(audio_file):
    os.remove(audio_file)


def call_speaker(text: str, config):
    engine = pyttsx4.init()
    change_voice(engine, config["language"], config["gender"])
    change_rate(engine, config["rate"])

    audio_file = tempfile.NamedTemporaryFile(suffix=".mp3", delete=False)
    engine.save_to_file(text, audio_file.name)
    engine.runAndWait()
    return audio_file


def change_voice(engine, language="en_US", gender='F'):
    voices = engine.getProperty('voices')
    gender_interpreter = {"M": "VoiceGenderMale", "F": "VoiceGenderFemale"}
    _gender = gender_interpreter[gender]
    for voice in voices:
        if language in voice.languages and _gender == voice.gender:
            engine.setProperty('voice', voice.id)
            return True

    gender_dict = {"VoiceGenderMale": "M", "VoiceGenderFemale": "F"}
    options = []
    for voice in voices:
        options.append((voice.languages[0], gender_dict[voice.gender]))
    raise RuntimeError(
        "Language '{}' for gender '{}' not found. Available options are \n {}".format(
            language, gender, options))


def change_rate(engine, rate=150):
    engine.setProperty('rate', rate)
