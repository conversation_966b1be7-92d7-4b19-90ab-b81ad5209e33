import logging
import threading


class MessagePipeline:
    def __init__(self, response, message_factory):
        self.response = response
        self.message_factory = message_factory

    def __str__(self):
        return f"Message(response={self.response}, mobile={self.message_factory.mobile})"


class Pipeline:
    """
    Class to allow a single element pipeline between producer and consumer.
    """

    def __init__(self):
        self.message = None
        self.producer_lock = threading.Lock()
        self.consumer_lock = threading.Lock()
        self.consumer_lock.acquire()  # initial state to start as producer produces many messages

    def consume_message(self, name) -> MessagePipeline:
        logging.debug("%s:waiting to consume", name)
        self.consumer_lock.acquire()
        logging.debug("%s:message consumed", name)
        message = self.message
        self.producer_lock.release()
        return message

    def produce_message(self, message: MessagePipeline, name):
        logging.debug("%s:waiting to produce", name)
        self.producer_lock.acquire()
        logging.debug("%s:message produced", name)
        self.message = message
        self.consumer_lock.release()
