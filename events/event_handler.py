import logging

from events.pipeline import <PERSON><PERSON><PERSON>, MessagePipeline
from openai import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from openai.types.beta import Assistant<PERSON><PERSON>amE<PERSON>
from openai.types.beta.threads import Message, MessageDelta
from openai.types.beta.threads.runs import Too<PERSON><PERSON><PERSON>, RunStep
from typing_extensions import override

from ai.openai_client import get_client_instance

client = get_client_instance()


class EventHandler(AssistantEventHandler):
    def __init__(self, thread_id, assistant_id, message_factory, cache_service, pipeline: Pipeline):
        super().__init__()
        self.output = None
        self.tool_id = None
        self.thread_id = thread_id
        self.assistant_id = assistant_id
        self.run_id = None
        self.run_step = None
        self.function_name = ""
        self.arguments = ""
        self.message_factory = message_factory
        self.pipeline = pipeline
        self.cache_service = cache_service

    @override
    def on_text_created(self, text) -> None:
        logging.debug(f"\nassistant on_text_created > ")

    @override
    def on_text_delta(self, delta, snapshot):
        logging.debug(f"\nassistant on_text_delta > {delta.value}")

    @override
    def on_end(self, ):
        logging.debug(f"\n end assistant > ", self.current_run_step_snapshot)

    @override
    def on_exception(self, exception: Exception) -> None:
        """Fired whenever an exception happens during streaming"""
        logging.debug(f"\nassistant > {exception}\n")

    @override
    def on_message_created(self, message: Message) -> None:
        logging.debug(f"\nassistant on_message_created > {message}\n")

    @override
    def on_message_done(self, message: Message) -> None:
        run_status = message.status
        if run_status == 'completed':
            logging.info("Message: %s", message)
            response = message.content[0].text.value
            new_message = MessagePipeline(response=response, message_factory=self.message_factory)
            self.pipeline.produce_message(message=new_message, name="MessageDone")
        else:
            logging.error("Failed Message: %s", message)

    @override
    def on_message_delta(self, delta: MessageDelta, snapshot: Message) -> None:
        logging.debug(f"\nassistant on_message_delta > {delta}\n")

    def on_tool_call_created(self, tool_call):
        logging.debug(f"\nassistant on_tool_call_created > {tool_call}")
        self.function_name = tool_call.function.name
        self.tool_id = tool_call.id
        logging.debug(f"\on_tool_call_created > run_step.status > {self.run_step.status}")

        logging.debug(f"\nassistant > {tool_call.type} {self.function_name}\n")

        keep_retrieving_run = client.beta.threads.runs.retrieve(
            thread_id=self.thread_id,
            run_id=self.run_id
        )

        while keep_retrieving_run.status in ["queued", "in_progress"]:
            keep_retrieving_run = client.beta.threads.runs.retrieve(
                thread_id=self.thread_id,
                run_id=self.run_id
            )

            logging.debug(f"\nSTATUS: {keep_retrieving_run.status}")

    @override
    def on_tool_call_done(self, tool_call: ToolCall) -> None:
        keep_retrieving_run = client.beta.threads.runs.retrieve(
            thread_id=self.thread_id,
            run_id=self.run_id
        )

        logging.debug(f"\nDONE STATUS: {keep_retrieving_run.status}")

        if keep_retrieving_run.status == "completed":
            all_messages = client.beta.threads.messages.list(
                thread_id=self.thread_id
            )

            logging.debug(all_messages.data[0].content[0].text.value, "", "")
            return

        elif keep_retrieving_run.status == "requires_action":
            logging.debug("here you would call your function")

        else:
            logging.debug(f"\nassistant on_tool_call_done > {tool_call}\n")

    @override
    def on_run_step_created(self, run_step: RunStep) -> None:
        # 2
        logging.debug(f"on_run_step_created")
        self.run_id = run_step.run_id
        self.run_step = run_step
        logging.debug("The type ofrun_step run step is ", type(run_step))
        logging.debug(f"\n run step created assistant > {run_step}\n")

    @override
    def on_run_step_done(self, run_step: RunStep) -> None:
        total_tokens_usage = run_step.usage.total_tokens
        cache_data = self.cache_service.get_cache_value(self.message_factory.hash)
        sum_token_usage = cache_data['token_usage'] + total_tokens_usage
        self.cache_service.set_cache_value(
            key=self.message_factory.hash,
            thread_id=cache_data['thread_id'],
            conversation_id=cache_data['conversation_id'],
            token_usage=sum_token_usage,
            update_conversation=cache_data['update_conversation']
        )

    @override
    def on_tool_call_delta(self, delta, snapshot):
        if delta.type == 'function':
            logging.debug(delta.function.arguments)
            self.arguments += delta.function.arguments
        elif delta.type == 'code_interpreter':
            logging.debug(f"on_tool_call_delta > code_interpreter")
            if delta.code_interpreter.input:
                logging.debug(delta.code_interpreter.input, end="")
            if delta.code_interpreter.outputs:
                logging.debug(f"\n\noutput >")
                for output in delta.code_interpreter.outputs:
                    if output.type == "logs":
                        logging.debug(f"\n{output.logs}")
        else:
            logging.debug(delta)

    @override
    def on_event(self, event: AssistantStreamEvent) -> None:
        if event.event == "thread.run.requires_action":
            logging.debug("\nthread.run.requires_action > submit tool call")
            logging.debug(f"ARGS: {self.arguments}")
