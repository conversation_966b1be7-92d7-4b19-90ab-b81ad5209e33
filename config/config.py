import os
from dataclasses import dataclass

_config_instance = None


@dataclass
class Configuration:
    mongo_uri: str
    mongo_host: str
    mongo_db: str
    mongo_user: str
    mongo_password: str
    url_crm: str
    url_spider: str
    sendgrid_api_key: str
    redis_host: str
    redis_port: int
    redis_username: str
    redis_password: str
    rabbitmq_host: str
    rabbitmq_user: str
    rabbitmq_password: str

    def __init__(self):
        self.mongo_uri = os.environ.get('MONGO_URI')
        self.mongo_host = os.environ.get('MONGO_HOST')
        self.mongo_db = os.environ.get('MONGO_DB')
        self.mongo_user = os.environ.get('MONGO_USER')
        self.mongo_password = os.environ.get('MONGO_PASSWORD')
        self.url_crm = os.environ.get('URL_CRM')
        self.url_spider = os.environ.get('URL_SPIDER')
        self.sendgrid_api_key = os.environ.get('SENDGRID_API_KEY')
        self.redis_host = os.environ.get('REDIS_HOST')
        self.redis_port = os.environ.get('REDIS_PORT')
        self.redis_username = os.environ.get('REDIS_USERNAME')
        self.redis_password = os.environ.get('REDIS_PASSWORD')
        self.auth_token = os.environ.get('AUTH_TOKEN')
        self.rabbitmq_host = os.environ.get('RABBITMQ_HOST')
        self.rabbitmq_user = os.environ.get('RABBITMQ_USER')
        self.rabbitmq_password = os.environ.get('RABBITMQ_PASSWORD')


def get_config_instance():
    global _config_instance
    if _config_instance is None:
        _config_instance = Configuration()
    return _config_instance
