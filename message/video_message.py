import logging
from message.base_message import BaseMessageInput, BaseMessageOutput


class VideoMessageInput(BaseMessageInput):
    def handle(self, stop_bot):
        video = self.connector.get_video(self.message_factory.data)
        video_id, mime_type = video["id"], video["mime_type"]
        video_url = self.connector.query_media_url(video_id)
        video_filename = self.connector.download_media(video_url, mime_type)
        logging.info(f"{self.message_factory.mobile} sent video {video_filename}")


class VideoMessageOutput(BaseMessageOutput):
    def send_message_to_connector(self, message, mobile):
        # Implementar lógica para enviar mensaje de texto al conector
        pass
