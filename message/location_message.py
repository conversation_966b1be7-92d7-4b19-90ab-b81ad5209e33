import logging
from message.base_message import BaseMessageInput, BaseMessageOutput


class LocationMessageInput(BaseMessageInput):
    def handle(self, stop_bot):
        message_location = self.connector.get_location(self.message_factory.data)
        message_latitude = message_location["latitude"]
        message_longitude = message_location["longitude"]
        logging.info("Location: %s, %s", message_latitude, message_longitude)


class LocationMessageOutput(BaseMessageOutput):
    def send_message_to_connector(self, message, mobile):
        # Implementar lógica para enviar mensaje de texto al conector
        pass
