import logging
from datetime import datetime

from ai.assistant import <PERSON><PERSON><PERSON><PERSON>
from enums.message_types import RoleMessageType
from events.event_types import RECEIVED_MESSAGE
from lead.lead_manager import Lead<PERSON>anager
from message.factory.message_factory import MessageFactory
from producers.event_producer import send_event
from services.cache import CacheService
from services.conversation import ConversationService
from utils.session_timer import SessionTimer


class BaseMessageInput:
    """
    Base class for handling incoming messages.
    Provides common functionality for different message types.
    """
    def __init__(
        self, 
        session_timer: SessionTimer,
        conversations_service: ConversationService,
        pipeline,
        message_factory: MessageFactory,
        cache_service: CacheService
    ):
        """
        Initialize the base message input handler.
        
        Args:
            session_timer: For managing session state and timeouts
            conversations_service: Service for conversation operations
            pipeline: Message processing pipeline
            message_factory: Factory for creating message objects
            cache_service: Service for caching conversation data
        """
        self.session_timer = session_timer
        self.conversations_service = conversations_service
        self.pipeline = pipeline
        self.message_factory = message_factory
        self.hash = message_factory.hash
        self.connector = message_factory.connector
        self.cache_service = cache_service
        self.conversation_db = None
        self.logger = logging.getLogger(__name__)

    def _create_new_thread(self):
        """
        Create a new conversation thread with the assistant.
        
        Initializes a new thread and sets up the initial cache entry.
        """
        assistant = AssistantManager.get_assistant_by_id(self.message_factory.assistant_id)
        thread_id = assistant.create_thread().id
        conversation_id = ''
        self.cache_service.set_cache_value(
            key=self.hash,
            thread_id=thread_id,
            conversation_id=conversation_id,
            token_usage=0,
            update_conversation=False
        )
        first_message = f"Mi nombre es {self.message_factory.name} y mi telefono es {self.message_factory.mobile}"
        assistant.create_user_message(
            thread_id=thread_id,
            message_content=first_message,
            role=RoleMessageType.USER
        )

    def handle(self, stop_bot):
        raise NotImplementedError("Subclasses must implement handle method.")

    def process_and_run_assistant(self, message):
        final_message = self.message_factory.before_instruction_prompt + message + self.message_factory.after_instruction_prompt
        self.run_assistant(message=final_message)

    def add_message_to_chathistory(self, message, conversation_id):
        new_message = {"role": RoleMessageType.USER.to_string(), "content": message}
        self.conversations_service.update_chat_history_in_conversation(
            conversation_id=conversation_id,
            new_message=new_message
        )
        return None

    def send_notification_new_message_to_crm(self, message):
        message = {
            "assign": self.conversation_db.assign,
            "leadId": self.conversation_db.id,
            "senderName": self.conversation_db.lead_name,
            "content": message,
        }
        send_event(event_type=RECEIVED_MESSAGE, payload={
            "payload": message
        })
        logging.info("Send crm new message notification")

    def initialize_conversation(self):
        """
        Initialize or retrieve an existing conversation.
        
        Returns:
            bool: True if bot should stop processing (botstop active), False otherwise
        """
        try:
            self.conversation_db = self.conversations_service.get_conversation_by_hash(self.hash)

            # If conversation exists and has botstop enabled, stop processing
            if self.conversation_db is not None and self.conversation_db.botstop:
                self.logger.info(f"Conversation {self.hash} has botstop enabled")
                return True

            cache_data = self.cache_service.get_cache_value(self.hash)

            if cache_data is not None:
                self.cache_service.set_cache_value(
                    key=self.hash,
                    thread_id=cache_data.get('thread_id'),
                    conversation_id=cache_data.get('conversation_id'),
                    token_usage=cache_data.get('token_usage', 0),
                    update_conversation=True
                )
                
                self.session_timer.create_session(
                    session_id=self.hash,
                    ttl_seconds=3600,
                    last_activity=datetime.now(),
                    cache_data=cache_data
                )
                
                self.logger.debug(f"Session recovered from cache: {self.hash}")
            else:
                self._create_new_thread()
                
                self.session_timer.create_session(
                    session_id=self.hash,
                    ttl_seconds=3600,
                    last_activity=datetime.now()
                )
                
                self.logger.debug(f"New session created: {self.hash}")

            return False
            
        except Exception as e:
            self.logger.error(
                f"Error initializing conversation {self.hash}: {str(e)}",
                exc_info=True
            )
            return False

    def run_assistant(self, message):
        cache_data = self.cache_service.get_cache_value(self.hash)
        token_usage: int = cache_data['token_usage']
        if token_usage > self.message_factory.conversation_max_tokens_usage:
            return
        try:
            thread_id = cache_data['thread_id']
            assistant = AssistantManager.get_assistant_by_id(self.message_factory.assistant_id)

            assistant.create_user_message(thread_id=thread_id, message_content=message, role=RoleMessageType.USER)
            assistant.run_assistant_with_stream(
                thread_id=thread_id,
                message_factory=self.message_factory,
                cache_service=self.cache_service,
                pipeline=self.pipeline
            )
        except Exception as e:
            logging.error("Error occurred while processing message: %s", str(e))
            return

class BaseMessageOutput:
    """
    Base class for handling outgoing messages.
    Provides common functionality for different message types.
    """
    def __init__(
            self,
            session_timer: SessionTimer,
            conversations_service: ConversationService,
            message_factory: MessageFactory,
            cache_service: CacheService
    ):
        """
        Initialize the base message output handler.
        
        Args:
            session_timer: For managing session state and timeouts
            conversations_service: Service for conversation operations
            message_factory: Factory for creating message objects
            cache_service: Service for caching conversation data
        """
        self.session_timer = session_timer
        self.conversations_service = conversations_service
        self.connector = message_factory.connector
        self.hash = message_factory.hash
        self.message_factory = message_factory
        self.cache_service = cache_service
        self.logger = logging.getLogger(__name__)

    def send_message_to_connector(self, message, mobile):
        raise NotImplementedError("Subclasses must implement handle method.")

    def schedule_delete(self, mobile, delay):
        try:
            return self.session_timer.set_timer(
                session_id=self.hash,
                timeout=delay,
                callback=self.delete_conversation_and_generate_lead,
                mobile=mobile
            )
        except Exception as e:
            self.logger.error(
                f"Error scheduling deletion for {mobile}: {str(e)}",
                exc_info=True
            )
            return False

    def delete_conversation_and_generate_lead(self, mobile):
        try:
            cache_data = self.cache_service.get_cache_value(self.hash) or {}
            
            lead_manager = LeadManager(
                session_timer=self.session_timer,
                conversations_service=self.conversations_service,
                connector=self.connector,
                message_factory=self.message_factory,
                cache_service=self.cache_service,
                cache_data=cache_data
            )

            update_conversation = cache_data.get('update_conversation', False)
            lead_manager.generate_or_update_lead(update_conversation=update_conversation)
            
            self.cache_service.delete_cache_value(key=self.hash)
            
            self.logger.info(f"Lead generated and local cache cleared for {mobile}")

        except Exception as e:
            self.logger.error(
                f"Error generating lead for {mobile}: {str(e)}",
                exc_info=True
            )
            raise