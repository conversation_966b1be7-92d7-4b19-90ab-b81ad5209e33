import logging
import threading
from ai.assistant import <PERSON><PERSON>anager
from enums.message_types import RoleMessageType
from lead.lead_manager import <PERSON><PERSON><PERSON><PERSON>
from message.factory.message_factory import MessageFactory
from metrics import GENERATE_LEAD_ERROR
from producers.event_producer import send_event

class BaseMessageInput:
    def __init__(self, conversations, conversations_service, pipeline, message_factory: MessageFactory, cache_service):
        self.conversations = conversations
        self.conversations_service = conversations_service
        self.pipeline = pipeline
        self.message_factory = message_factory
        self.hash = message_factory.hash
        self.connector = message_factory.connector
        self.cache_service = cache_service
        self.conversation_db = None

    def _create_new_thread(self, assistant):
        thread_id = assistant.create_thread().id
        conversation_id = ''
        self.cache_service.set_cache_value(
            key=self.hash,
            thread_id=thread_id,
            conversation_id=conversation_id
        )
        first_message = f"Mi nombre es {self.message_factory.name} y mi telefono es {self.message_factory.mobile}"
        assistant.create_user_message(
            thread_id=thread_id,
            message_content=first_message,
            role=RoleMessageType.USER
        )
        return thread_id, conversation_id

    def handle(self, stop_bot):
        raise NotImplementedError("Subclasses must implement handle method.")

    def process_and_run_assistant(self, message):
        final_message = self.message_factory.before_instruction_prompt + message + self.message_factory.after_instruction_prompt
        self.run_assistant(message=final_message)

    def cancel_existing_timer(self):
        if self.conversations[self.hash]['timer'] is not None:
            self.conversations[self.hash]['timer'].cancel()
            self.conversations[self.hash]['timer'] = None

    def add_message_to_chathistory(self, message, conversation_id):
        new_message = {"role": RoleMessageType.USER.to_string(), "content": message}
        self.conversations_service.update_chat_history_in_conversation(
            conversation_id=conversation_id,
            new_message=new_message
        )
        return None

    def send_notification_new_message_to_crm(self, message):
        message = {
            "assign": self.conversation_db.assign,
            "leadId": self.conversation_db.id,
            "senderName": self.conversation_db.lead_name,
            "content": message,
        }
        send_event(event_type="crm", payload={
            "payload": message
        })
        logging.info("Send crm new message notification")

    def initialize_conversation(self):
        assistant = AssistantManager.get_assistant_by_id(self.message_factory.assistant_id)
        self.conversation_db = self.conversations_service.get_conversation_by_hash(self.hash)

        # If exists conversation in DB and BotStop, return immediately after save conversation
        if self.conversation_db is not None and self.conversation_db.botstop:
            return True

        cache_data = self.cache_service.get_cache_value(self.hash)

        if cache_data is not None:
            thread_id = cache_data['thread_id']
            update_conversation = True
            conversation_id = cache_data['conversation_id']
        else:
            thread_id, conversation_id = self._create_new_thread(assistant)
            update_conversation = False

        self.conversations[self.hash] = {
            'timer': None,
            'thread_id': thread_id,
            'update_conversation': update_conversation,
            'conversation_id': conversation_id,
            'token_usage': 0
        }

        return False

    def delete_conversation(self):
        if self.hash in self.conversations:
            try:
                del self.conversations[self.hash]
                self.cache_service.delete_cache_value(key=self.hash)
                logging.info(f"Deleted conversation for mobile {self.hash}")
            except Exception as e:
                logging.error(f"Error in generate lead for mobile {self.hash}: {str(e)}\n")
        else:
            logging.info(f"No conversation found for mobile {self.hash}")

    def run_assistant(self, message):
        if self.conversations[self.hash]['token_usage'] > self.message_factory.conversation_max_tokens_usage:
            return
        try:
            thread_id = self.conversations[self.hash]['thread_id']
            assistant = AssistantManager.get_assistant_by_id(self.message_factory.assistant_id)

            assistant.create_user_message(thread_id=thread_id, message_content=message, role=RoleMessageType.USER)
            assistant.run_assistant_with_stream(
                thread_id=thread_id,
                message_factory=self.message_factory,
                conversations=self.conversations,
                pipeline=self.pipeline
            )
        except Exception as e:
            logging.error("Error occurred while processing message: %s", str(e))
            return

    def delete_conversation_and_generate_lead(self):
        # TODO en un futuro revisar si esto se puede eliminar ya que es codigo duplicado
        if self.hash in self.conversations:
            try:
                lead_manager = LeadManager(
                    message_factory=self.message_factory,
                    conversations=self.conversations,
                    conversations_service=self.conversations_service,
                    connector=self.connector,
                    cache_service=self.cache_service
                )
                update_conversation = self.conversations[self.hash]['update_conversation']
                lead_manager.generate_or_update_lead(update_conversation)
            except Exception as e:
                GENERATE_LEAD_ERROR.inc()
                logging.error(f"Error in generate lead for mobile {self.hash}: {str(e)}\n")
            finally:
                del self.conversations[self.hash]
                logging.info(f"Deleted conversation for mobile {self.hash}")
        else:
            logging.info(f"No conversation found for mobile {self.hash}")


class BaseMessageOutput:
    def __init__(self, conversations, conversations_service, message_factory, cache_service):
        self.conversations = conversations
        self.conversations_service = conversations_service
        self.connector = message_factory.connector
        self.hash = message_factory.hash
        self.message_factory = message_factory
        self.cache_service = cache_service

    def send_message_to_connector(self, message, mobile):
        raise NotImplementedError("Subclasses must implement handle method.")

    def schedule_delete(self, mobile, delay):
        if self.hash in self.conversations and self.conversations[self.hash]['timer'] is None:
            self.conversations[self.hash]['timer'] = threading.Timer(delay, self.delete_conversation_and_generate_lead)
            self.conversations[self.hash]['timer'].start()
            logging.info(f"Scheduled conversation deletion for mobile {mobile} in {delay} seconds")
        else:
            logging.info(f"No conversation found for mobile {mobile} or timer already exists")

    def delete_conversation_and_generate_lead(self):
        if self.hash in self.conversations:
            try:
                lead_manager = LeadManager(
                    message_factory=self.message_factory,
                    conversations=self.conversations,
                    conversations_service=self.conversations_service,
                    connector=self.connector,
                    cache_service=self.cache_service
                )
                update_conversation = self.conversations[self.hash]['update_conversation']
                lead_manager.generate_or_update_lead(update_conversation)
            except Exception as e:
                GENERATE_LEAD_ERROR.inc()
                logging.error(f"Error in generate lead for mobile {self.hash}: {str(e)}\n")
            finally:
                del self.conversations[self.hash]
                logging.info(f"Deleted conversation for mobile {self.hash}")
        else:
            logging.info(f"No conversation found for mobile {self.hash}")
