import logging
from message.base_message import BaseMessageInput, BaseMessageOutput


class ImageMessageInput(BaseMessageInput):
    def handle(self):
        image = self.connector.get_image(self.message_factory.data)
        image_id, mime_type = image["id"], image["mime_type"]
        image_url = self.connector.query_media_url(image_id)
        image_filename = self.connector.download_media(image_url, mime_type)
        logging.info(f"{self.message_factory.mobile} sent image {image_filename}")


class ImageMessageOutput(BaseMessageOutput):
    def send_message_to_connector(self, message, mobile):
        # Implementar lógica para enviar mensaje de texto al conector
        pass
