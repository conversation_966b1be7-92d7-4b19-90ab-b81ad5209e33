"""
Message Handler - Manages the processing of incoming and outgoing messages.
Handles message routing, threading, and error handling in a thread-safe manner.
"""
import logging
from concurrent.futures import ThreadPoolExecutor
from dotenv import load_dotenv

from events.pipeline import <PERSON>peline, MessagePipeline
from message.audio_message import AudioMessageInput, AudioMessageOutput
from message.document_message import DocumentMessageInput, DocumentMessageOutput
from message.factory.message_factory import MessageFactory
from message.image_message import ImageMessageInput, ImageMessageOutput
from message.interactive_message import InteractiveMessageInput, InteractiveMessageOutput
from message.location_message import LocationMessageInput, LocationMessageOutput
from message.text_message import TextMessageInput, TextMessageOutput
from message.video_message import VideoMessageInput, VideoMessageOutput
from services.cache import CacheService
from services.conversation import ConversationService
from utils.session_timer import SessionTimer

load_dotenv()

MESSAGE_HANDLERS_INPUT = {
    "text": TextMessageInput,
    "ciphertext": TextMessageInput,
    "interactive": InteractiveMessageInput,
    "location": LocationMessageInput,
    "image": ImageMessageInput,
    "video": VideoMessageInput,
    "audio": AudioMessageInput,
    "document": DocumentMessageInput,
}

MESSAGE_HANDLERS_OUTPUT = {
    "text": TextMessageOutput,
    "ciphertext": TextMessageOutput,
    "interactive": InteractiveMessageOutput,
    "location": LocationMessageOutput,
    "image": ImageMessageOutput,
    "video": VideoMessageOutput,
    "audio": AudioMessageOutput,
    "document": DocumentMessageOutput,
}

SENTINEL = object()


class MessageHandler:
    def __init__(
            self,
            session_timer: SessionTimer,
            conversations_service: ConversationService,
            cache_service: CacheService
    ):
        """
        Initialize the MessageHandler with required services.
        
        Args:
            session_timer: Session management for tracking conversation state
            conversations_service: Service for conversation-related operations
            cache_service: Service for caching conversation data
        """
        self.session_timer = session_timer
        self.conversations_service = conversations_service
        self.cache_service = cache_service
        self.executor = ThreadPoolExecutor(max_workers=10)
        self.logger = logging.getLogger(__name__)
        self.pipeline = Pipeline()
        self._start_consumer()

    def __del__(self):
        try:
            self.logger.info("Closing MessageHandler's ThreadPoolExecutor")
            self.executor.shutdown(wait=True)
        except Exception as e:
            self.logger.error(
                "Error closing ThreadPoolExecutor",
                exc_info=True
            )

    def handle(self, message_factory: MessageFactory) -> None:
        """
        Handle an incoming message by dispatching it to the appropriate handler.
        
        Args:
            message_factory: Factory containing message data and metadata
        """
        message_type = message_factory.message_type
        handler_input = MESSAGE_HANDLERS_INPUT.get(message_type)

        try:
            self.executor.submit(
                self._process_message,
                handler_input,
                message_factory
            )
            
        except Exception as e:
            self.logger.error(
                f"Error processing message of type {message_type}: {str(e)}",
                exc_info=True
            )

    def _process_message(self, handler_input, message_factory):
        """
        Process a message using the appropriate input handler.
        
        Args:
            handler_input: The input handler class for the message type
            message_factory: Factory containing message data and metadata
            
        Raises:
            Exception: If there's an error during message processing
        """
        try:
            input_handler = handler_input(
                session_timer=self.session_timer,
                conversations_service=self.conversations_service,
                pipeline=self.pipeline,
                message_factory=message_factory,
                cache_service=self.cache_service
            )
            
            stop_bot = False
            
            session_data = self.session_timer.get_session(message_factory.hash)
            if session_data is not None:
                self.session_timer.delete_session(message_factory.hash)
            else:
                if input_handler.initialize_conversation():
                    stop_bot = True
            
            if not stop_bot:
                input_handler.handle(stop_bot)
            
        except Exception as e:
            self.logger.error(
                f"Error processing message for {message_factory.mobile}: {str(e)}",
                exc_info=True
            )
            raise

    def _start_consumer(self):
        """Start the message consumer in a background thread."""
        self.executor.submit(self._consume_messages)

    def _consume_messages(self):
        """
        Continuously consume messages from the pipeline and process them.
        
        This runs in a separate thread and processes output messages
        that need to be sent back to users.
        """
        message = None
        while message is not SENTINEL:
            message = self.pipeline.consume_message("Consumer")
            if message is not SENTINEL:
                self._process_output_message(message)
        return

    def _process_output_message(self, message: MessagePipeline):
        """
        Process an output message by sending it through the appropriate handler.
        
        Args:
            message: The message pipeline object containing response and metadata
            
        Raises:
            Exception: If there's an error processing the output message
        """
        try:
            message_output = MESSAGE_HANDLERS_OUTPUT.get(message.message_factory.message_type)
            if message_output:
                handler_obj = message_output(
                    session_timer=self.session_timer,
                    conversations_service=self.conversations_service,
                    message_factory=message.message_factory,
                    cache_service=self.cache_service
                )
                
                handler_obj.send_message_to_connector(
                    message=message.response, 
                    mobile=message.message_factory.mobile
                )
                
                self.logger.info(
                    f"Message sent to {message.message_factory.mobile}: "
                    f"{str(message.response)[:100]}..."
                )
            else:
                self.logger.error(
                    f"No handler found for message type: "
                    f"{message.message_factory.message_type}"
                )
                
        except Exception as e:
            self.logger.error(
                f"Error processing output message: {str(e)}",
                exc_info=True
            )
            raise
