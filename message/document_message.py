import logging
from message.base_message import BaseMessageInput, BaseMessageOutput


class DocumentMessageInput(BaseMessageInput):
    def handle(self):
        file = self.connector.get_document(self.message_factory.data)
        file_id, mime_type = file["id"], file["mime_type"]
        file_url = self.connector.query_media_url(file_id)
        file_filename = self.connector.download_media(file_url, mime_type)
        logging.info(f"{self.message_factory.mobile} sent file {file_filename}")


class DocumentMessageOutput(BaseMessageOutput):
    def send_message_to_connector(self, message, mobile):
        # Implementar lógica para enviar mensaje de texto al conector
        pass
