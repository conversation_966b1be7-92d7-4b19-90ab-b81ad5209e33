import logging
from message.base_message import BaseMessageInput, BaseMessageOutput


class InteractiveMessageInput(BaseMessageInput):
    def handle(self, stop_bot):
        message_response = self.connector.get_interactive_response(self.message_factory.data)
        interactive_type = message_response.get("type")
        message_id = message_response[interactive_type]["id"]
        message_text = message_response[interactive_type]["title"]
        logging.info(f"Interactive Message; {message_id}: {message_text}")


class InteractiveMessageOutput(BaseMessageOutput):
    def send_message_to_connector(self, message, mobile):
        # Implementar lógica para enviar mensaje de texto al conector
        pass
