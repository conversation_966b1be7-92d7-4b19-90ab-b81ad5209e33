import logging

from message.base_message import BaseMessageInput, BaseMessageOutput


class TextMessageInput(BaseMessageInput):
    def handle(self, stop_bot):
        message = self.connector.get_message(self.message_factory.data)
        if stop_bot:
            self.add_message_to_chathistory(
                message=message,
                conversation_id=self.conversation_db.id 
            )
            self.send_notification_new_message_to_crm(
                message=message
            )
            return

        self.process_and_run_assistant(message=message)


class TextMessageOutput(BaseMessageOutput):
    def send_message_to_connector(self, message, mobile):
        try:
            logging.info(f"Message Response; {message}")
            self.connector.send_message(message, mobile)
        except Exception as e:
            logging.error(f"Error send message in connector: {str(e)}")

        # Schedule conversation deletion after 30 minutes
        self.schedule_delete(mobile, self.message_factory.finish_conversation_time_in_seconds)
