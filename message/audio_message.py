import logging

from ai.llm import text_to_speech, delete_audio_file
from ai.whisper import speech_to_text, AudioTooLongException
from message.base_message import BaseMessageInput, BaseMessageOutput

DEFAULT_FINISH_CONVERSATION_TIME_IN_SECONDS = 1800


class AudioMessageInput(BaseMessageInput):
    def handle(self, stop_bot):
        audio_filename = self.connector.get_audio(self.message_factory.data)
        logging.info(f"{self.message_factory.mobile} sent audio {audio_filename}")
        try:
            transcribe_text = speech_to_text(audio_filename)
            self.process_and_run_assistant(message=transcribe_text)
        except AudioTooLongException as e:
            self.connector.message_type = 'text'
            self.connector.send_message(str(e), self.message_factory.mobile)


class AudioMessageOutput(BaseMessageOutput):
    def send_message_to_connector(self, message, mobile):
        try:
            if self.generate_audio():
                audio_file = text_to_speech(message)
                media_data = self.connector.upload_media(audio_file.name)
                attachment_id = media_data["id"]
                self.connector.send_audio(attachment_id, mobile, False)
                delete_audio_file(audio_file.name)
            else:
                self.connector.message_type = 'text'
                logging.info(f"Message Response; {message}")
                self.connector.send_message(message, mobile)
        except Exception as e:
            logging.error(f"Error during text-to-speech conversion: {str(e)}")
            # En caso de error, enviar el mensaje de texto sin convertir
            self.connector.message_type = 'text'
            self.connector.send_message(message, mobile)

        # Schedule conversation deletion after 30 minutes
        self.schedule_delete(mobile, DEFAULT_FINISH_CONVERSATION_TIME_IN_SECONDS)

    def generate_audio(self):
        # Cada 3 mensajes un audio.
        # return len(self.conversations[self.mobile]['conversation']) % 3 == 0
        # Saludo inicial
        # return len(self.conversations[self.mobile]['conversation']) == 4
        return False
