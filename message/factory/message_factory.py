DEFAULT_FINISH_CONVERSATION_TIME_IN_SECONDS = 1800
DEFAULT_CONVERSATIONS_MAX_TOKENS_USAGE = 100000


def get_finish_conversation_time(finish_conversation_time_in_seconds):
    if finish_conversation_time_in_seconds is None or finish_conversation_time_in_seconds == 0:
        return DEFAULT_FINISH_CONVERSATION_TIME_IN_SECONDS
    else:
        return finish_conversation_time_in_seconds


def get_conversation_max_tokens_usage(conversation_max_tokens_usage):
    if conversation_max_tokens_usage is None or conversation_max_tokens_usage == 0:
        return DEFAULT_CONVERSATIONS_MAX_TOKENS_USAGE
    else:
        return conversation_max_tokens_usage


class MessageFactory:
    def __init__(self, connector, data, mobile, name, message_type, client_data_dto):
        client_id = client_data_dto.get("_id")
        before_instruction_prompt = client_data_dto.get("before_instruction_prompt")
        after_instruction_prompt = client_data_dto.get("after_instruction_prompt")
        finish_conversation_time_in_seconds = client_data_dto.get("finish_conversation_time")
        conversation_max_tokens_usage = client_data_dto.get("conversation_max_tokens_usage")
        self.hash = mobile + client_id
        self.connector = connector
        self.data = data
        self.mobile = mobile
        self.name = name
        self.message_type = message_type
        self.wa_id = mobile
        self.client_id = client_id
        self.assistant_id = client_data_dto.get("assistant_id")
        self.finish_conversation_time_in_seconds = get_finish_conversation_time(finish_conversation_time_in_seconds)
        self.conversation_max_tokens_usage = get_conversation_max_tokens_usage(conversation_max_tokens_usage)
        self.before_instruction_prompt = before_instruction_prompt if before_instruction_prompt is not None else ''
        self.after_instruction_prompt = after_instruction_prompt if after_instruction_prompt is not None else ''
        self.finish_conversation_vars = client_data_dto.get("finish_conversation_vars")
        self.integrations = client_data_dto.get("integrations")
        self.client_columns = client_data_dto.get("columns")
        self.client_tags = client_data_dto.get("tags")
