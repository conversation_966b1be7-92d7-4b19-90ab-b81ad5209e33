from mongoengine import Document, IntField, BooleanField


class User(Document):
    clientid = IntField()
    active = BooleanField()
    role = StringField()

    meta = {'collection': 'users'}

    @staticmethod
    def build_to_json(json_data):
        user = User(
            clientid=json_data["clientid"],
            active=json_data["active"],
            role=json_data["role"],
        )
        return user.to_mongo().to_dict()
