from mongoengine import Document, EmbeddedDocument, <PERSON><PERSON>ield, DateTimeField, EmbeddedDocumentListField, \
    EmbeddedDocumentField, IntField, BooleanField, DictField, ListField

from utils.utils import get_current_time


class Lead(EmbeddedDocument):
    fullname = StringField()
    email = StringField()
    phone = StringField()
    data = DictField()
    hash = StringField()

class Message(EmbeddedDocument):
    role = StringField()
    content = StringField()


class Conversation(Document):
    chathistory = EmbeddedDocumentListField(Message)
    active = BooleanField()
    status = StringField()
    lead = EmbeddedDocumentField(Lead)
    clientid = IntField()
    summary = StringField()
    nextContact = DateTimeField(default=get_current_time)
    createdAt = DateTimeField(default=get_current_time)
    updatedAt = DateTimeField(default=get_current_time)
    origin = StringField()
    origin_id = IntField()
    assign = StringField()
    tokenusage = IntField()
    beforeinstructionprompt = StringField()
    afterinstructionprompt = StringField()
    botstop = BooleanField()
    additional_fields = DictField()
    tags = ListField(StringField())
    columns_ids = ListField(StringField())

    meta = {'collection': 'conversations'}

    @staticmethod
    def build_to_json(json_data):
        lead = Lead(
            fullname=json_data["name"],
            email=json_data["email"],
            phone=json_data["mobile"]
        )
        conversation = Conversation(
            chathistory=json_data["conversation"],
            active=json_data['active'] if 'active' in json_data else True,
            status=json_data['status'] if 'status' in json_data else "nuevo",
            lead=lead,
            clientid=json_data["client_id"],
            summary=json_data["summary"],
            origin="web-popup",
            origin_id=4,
            botstop=json_data["botstop"],
            additional_fields=json_data["additional_fields"]
        )
        return conversation.to_mongo().to_dict()
