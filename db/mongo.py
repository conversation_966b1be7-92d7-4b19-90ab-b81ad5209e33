import logging

from bson import ObjectId
from pymongo import MongoClient


class MongoDatabase:
    def __init__(self, uri, username, password, host, db_name, collection_name):
        uri = f"{uri}://{username}:{password}@{host}/{db_name}"
        self.client = MongoClient(uri)
        self.db = self.client[db_name]
        self.collection = self.db[collection_name]

    def insert_document(self, document):
        try:
            result = self.collection.insert_one(document)
            logging.debug(f"Insert result: acknowledged={result.acknowledged}, inserted_id={result.inserted_id}")
            if result.acknowledged:
                logging.debug(f"Insertion successful in DB for register with id {result.inserted_id}")
                return result.inserted_id
            else:
                logging.error("Insertion failed in DB")
        except Exception as e:
            logging.error(f"Error during insertion: {str(e)}")

    def get_document(self, key):
        try:
            client_id = ObjectId(key)
            query = {"_id": client_id}
            document = self.collection.find_one(query)
            if document:
                logging.debug(f"Document found for id {key}")
                return document
            else:
                logging.debug(f"Document not found for id {key}")
                return None
        except Exception as e:
            logging.error(f"Error during document retrieval: {str(e)}")
            return None

    def get_documents_by_filter(self, query, proyections, sortField=None, sortValue=None, limit=0):
        try:
            if sortField is not None:
                documents = list(self.collection.find(query, proyections).sort(sortField, sortValue).limit(limit))
            else:
                documents = list(self.collection.find(query, proyections))

            if documents:
                logging.debug(f"{len(documents)} documents found in DB")
                return documents
            else:
                logging.debug("No documents found")
                return None
        except Exception as e:
            logging.error(f"Error during document retrieval: {str(e)}")
            return None

    def update_many(self, query, update):
        try:
            result = self.collection.update_many(query, update)
            if result is not None and hasattr(result, 'modified_count'):
                logging.debug(
                    f"Update result: acknowledged={result.acknowledged}, modified_count={result.modified_count}")
                if result.acknowledged:
                    if result.modified_count > 0:
                        logging.debug(f"Update successful in DB: {result.modified_count} documents modified")
                    else:
                        logging.debug("No documents were modified")
                else:
                    logging.error("Update failed")
            else:
                logging.error("Update failed: Invalid result")
        except Exception as e:
            logging.error(f"Error during update: {str(e)}")
