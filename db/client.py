from mongoengine import Document, <PERSON><PERSON>ield, BooleanField, EmbeddedDocumentListField, EmbeddedDocument

class Column(EmbeddedDocument):
    id = StringField()
    name = StringField()

class Client(Document):
    name = StringField()
    prompt = StringField()
    active = BooleanField()
    botName = StringField()
    botDescription = StringField()
    botAvatar = StringField()
    token = StringField()
    fbAccesToken = StringField()
    phoneNumberId = StringField()
    promptwpp = StringField()
    columns = EmbeddedDocumentListField(Column)

    meta = {'collection': 'clients'}

    @staticmethod
    def build_to_json(json_data):
        client = Client(
            name=json_data["name"],
            prompt=json_data["prompt"],
            active=json_data["active"],
            botName=json_data["botName"],
            botDescription=json_data["botDescription"],
            botAvatar=json_data["botAvatar"],
            token=json_data["token"],
            phoneNumberId=json_data["phoneNumberId"],
            promptwpp=json_data["promptwpp"],
            fbAccesToken=json_data["fbAccesToken"],
        )
        return client.to_mongo().to_dict()
