from enum import Enum


class ConnectorType(Enum):
    WHATSAPP_VENOM = "whatsapp_venom_connector"
    WHATSAPP_API = "whatsapp_api_connector"
    FACEBOOK = "facebook_connector"

    @staticmethod
    def from_origin_id(origin_id):
        """Devuelve el ConnectorType correspondiente al origin_id."""
        mapping = {
            1: ConnectorType.WHATSAPP_API,
            2: ConnectorType.FACEBOOK,
            3: ConnectorType.WHATSAPP_VENOM
        }
        return mapping.get(origin_id, None)
