from flask import request, has_request_context

from broker.whatsapp_venom import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from broker.whatsapp_api import Whatsa<PERSON><PERSON><PERSON>
from enums.connector_types import ConnectorType


def init_brokers(client_service, connectors):
    brokers = {
        ConnectorType.WHATSAPP_API: WhatsappAPI(client_service, connectors),
        ConnectorType.WHATSAPP_VENOM: WhatsappVenom(client_service, connectors),
    }
    return brokers


class QueueManager:
    def __init__(self, client_service, connectors):
        self.brokers = init_brokers(client_service, connectors)

    def hook(self, request_data, connector_type):
        client_id = request_data.get('clientId')
        if not client_id and has_request_context():
            client_id = request.args.get("client.id")

        if not client_id:
            return "client.id parameter is missing", 400

        if connector_type not in self.brokers:
            return "Unsupported connector type", 400

        broker = self.brokers[connector_type]
        return broker.hook(request_data=request_data, client_id=client_id)
