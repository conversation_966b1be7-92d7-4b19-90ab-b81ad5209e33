import logging

from broker.base_broker import BaseQueue
from enums.connector_types import ConnectorType


class WhatsappAPI(BaseQueue):

    def extract_data(self, request_data):
        try:
            value = request_data['entry'][0]['changes'][0]['value']
            messages = value.get('messages')
            contacts = value.get('contacts')

            if messages and contacts:
                phone_number = contacts[0]['wa_id']
                message = messages[0]['text']['body']
                return phone_number, message
            else:
                logging.debug("Mensaje omitido debido a la falta de 'contacts' o 'messages': %s", value)
                return None, None
        except (KeyError, IndexError, TypeError) as e:
            logging.error(f"Error al extraer datos del mensaje: {e}")
            return None, None

    def hook(self, request_data, client_id):
        try:
            return self.hook_pass_conn(request_data=request_data, client_id=client_id, connector_type=ConnectorType.WHATSAPP_API)
        except Exception as e:
            logging.error(f"Error agregando el mensaje en el hook: {e}")
