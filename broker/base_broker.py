import logging
import queue
import threading

from dtos.client_data_dto import ClientDataDTO

QUEUE_TIME_PROCESS_MESSAGES = 5


class BaseQueue:
    def __init__(self, client_service, connectors):
        self.client_service = client_service
        self.phone_number_queues = {}
        self.phone_number_timers = {}
        self.connectors = connectors
        self.lock = threading.Lock()

    def extract_data(self, request_data):
        raise NotImplementedError("Subclasses must implement handle method.")

    def hook(self, request_data, client_id):
        raise NotImplementedError("Subclasses must implement handle method.")

    def process_messages(self, phone_number):
        with self.lock:
            try:
                phone_number_timer = self.phone_number_timers.pop(phone_number, None)
                if phone_number_timer:
                    phone_number_timer.cancel()

                phone_number_queue = self.phone_number_queues.pop(phone_number, None)
                if phone_number_queue:
                    queue_info = phone_number_queue.get()
                    client_data_dto = ClientDataDTO.build(queue_info['client_data'])
                    self.connectors[queue_info['connector_type']].process_queue_message(queue_info['data'], client_data_dto)
            except Exception as e:
                logging.error(f"Error procesando el mensaje de la cola: {e}")

    def hook_pass_conn(self, request_data, client_id, connector_type):
        phone_number, message = self.extract_data(request_data)
        if not phone_number or not message:
            return

        with self.lock:
            phone_number_queue = self.phone_number_queues.setdefault(phone_number, queue.Queue())
            phone_number_timer = self.phone_number_timers.pop(phone_number, None)

            if phone_number_timer is not None:
                try:
                    phone_number_timer.cancel()
                except Exception as e:
                    logging.error(f"Error cancelando el temporizador: {e}")

            existing_messages = list(phone_number_queue.queue)

            if existing_messages:
                existing_messages[0]["data"]["message"] = existing_messages[0]["data"]["message"] + ". " + message
                time_response_message = existing_messages[0]["client_data"]["time_response_message"]
            else:
                client_data = self.client_service.get_client_information(client_id)

                if client_data is None:
                    logging.error(f"Client data not found for client_id: {client_id}")
                    return "Client data not found", 404

                if "time_response_message" in client_data and isinstance(client_data["time_response_message"], int) and client_data["time_response_message"] != 0:
                    time_response_message = client_data["time_response_message"]
                else:
                    time_response_message = QUEUE_TIME_PROCESS_MESSAGES

                phone_number_queue.put({
                    "data": request_data,
                    "client_data": client_data,
                    "connector_type": connector_type,
                })

            phone_number_timer = threading.Timer(time_response_message, self.process_messages, args=(phone_number,))
            phone_number_timer.start()
            self.phone_number_timers[phone_number] = phone_number_timer
